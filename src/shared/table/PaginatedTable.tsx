import * as Styled from './style'
import * as SharedStyled from '../../styles/styled'
import { usePagination, useTable, useAsyncDebounce, useGlobalFilter, useFilters } from 'react-table'
import { forwardRef, Fragment, useEffect, useMemo, useState } from 'react'
import { colors } from '../../styles/theme'

import { useAppSelector } from '../../logic/redux/reduxHook'
import useDebounce from '../hooks/useDebounce'
import { setPaginationSettings } from '../../logic/redux/actions/pagination'
import { useDispatch } from 'react-redux'
import { useLocation } from 'react-router-dom'

interface I_Table {
  columns: any
  data: any
  totalItems?: number
  pageCount?: number
  // setPageIndex?: React.Dispatch<React.SetStateAction<number>>
  // setpageSize?: React.Dispatch<React.SetStateAction<number>>
  fetchData: ({ pageSize, pageIndex }: any) => void
  loading?: boolean
  onRowClick?: (val: any) => void
  isLoadMoreLoading?: boolean
  noBorder?: boolean
  padding?: string
  minWidth?: string
  noLink?: boolean
  hideHeader?: boolean
  pageKey?: string // Optional custom key for pagination settings

  //checkbox states
  selectable?: boolean
  selectedIds?: string[]
  setSelectedIds?: (ids: string[]) => void
  idField?: string
  //checkbox states
}

export const PaginatedTable = forwardRef((props: I_Table, ref: any) => {
  const {
    columns,
    data,
    loading,
    fetchData,
    onRowClick,
    totalItems = 0,
    pageCount = 0,
    // setPageIndex,
    // setpageSize,
    isLoadMoreLoading,
    noBorder,
    padding,
    minWidth,
    noLink,
    hideHeader,
    pageKey,

    //checkbox states
    selectable = false,
    selectedIds = [],
    setSelectedIds = () => {},
    idField = '_id',
    //checkbox states
  } = props

  const dispatch = useDispatch()
  const location = useLocation()

  const paginationKey = pageKey || location.pathname

  // Get saved pagination settings from Redux
  const paginationSettings = useAppSelector((state) => state.pagination.settings[paginationKey])
  const savedPageIndex = paginationSettings?.pageIndex ?? 0
  const savedPageSize = paginationSettings?.pageSize ?? 10

  const tableColumns = useMemo(() => {
    if (!selectable) return columns

    return [
      {
        Header: () => (
          <input
            type="checkbox"
            checked={data.length > 0 && data.every((row) => selectedIds.includes(row[idField]))}
            onChange={handleSelectAllClick}
            style={{ cursor: 'pointer' }}
          />
        ),
        accessor: 'selection',
        Cell: ({ row }: any) => (
          <input
            type="checkbox"
            checked={selectedIds.includes(row.original[idField])}
            onClick={(e) => handleCheckboxClick(e, row.original[idField])}
            style={{ cursor: 'pointer' }}
          />
        ),
        width: 40,
      },
      ...columns,
    ]
  }, [columns, selectable, data, selectedIds, idField])

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    rows,
    prepareRow,
    canPreviousPage,
    canNextPage,
    nextPage,
    previousPage,
    gotoPage,
    setPageSize,
    setGlobalFilter,
    state: { pageIndex, pageSize, globalFilter },
  }: any = useTable(
    {
      columns: tableColumns,
      data,
      initialState: {
        pageIndex: savedPageIndex,
        pageSize: savedPageSize,
      },
      manualPagination: true,
      pageCount,
      autoResetPage: false,
    },
    useFilters,
    useGlobalFilter,
    usePagination
  )

  const debouncedPageIndex = useDebounce(pageIndex, 500) // 500ms delay
  const debouncedPageSize = useDebounce(pageSize, 500)

  // Save pagination settings to Redux when they change
  useEffect(() => {
    dispatch(setPaginationSettings(paginationKey, debouncedPageIndex, debouncedPageSize))
  }, [debouncedPageIndex, debouncedPageSize, paginationKey, dispatch])

  useEffect(() => {
    // setPageIndex?.(debouncedPageIndex)
    // setpageSize?.(debouncedPageSize)
    fetchData({ pageIndex: debouncedPageIndex, pageSize: debouncedPageSize })
  }, [debouncedPageIndex, debouncedPageSize, fetchData])

  const start = Math.min(pageIndex * pageSize + 1, totalItems)
  const end = Math.min((pageIndex + 1) * pageSize, totalItems)

  const [inputPage, setInputPage] = useState(String(pageIndex + 1))

  useEffect(() => {
    setInputPage(String(pageIndex + 1))
  }, [pageIndex])

  const handleCheckboxClick = (e: React.MouseEvent, id: string) => {
    e.stopPropagation() // Prevent row click event

    if (selectedIds.includes(id)) {
      // Remove ID if already selected
      setSelectedIds(selectedIds.filter((selectedId) => selectedId !== id))
    } else {
      // Add ID if not selected
      setSelectedIds([...selectedIds, id])
    }
  }

  const handleSelectAllClick = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      // Select all visible rows
      const allIds = data.map((row) => row[idField])
      setSelectedIds(allIds)
    } else {
      // Deselect all
      setSelectedIds([])
    }
  }

  return (
    <>
      <Styled.Pagination>
        <Styled.ShowEntriesDiv>
          <Styled.PageSpan>
            {start}-{end} of {totalItems}
          </Styled.PageSpan>

          <Styled.ShowEntriesDiv>
            <Styled.IconContainer onClick={() => gotoPage(0)} disabled={!canPreviousPage}>
              ⏮
            </Styled.IconContainer>

            <Styled.IconContainer onClick={previousPage} disabled={!canPreviousPage}>
              ◀
            </Styled.IconContainer>
          </Styled.ShowEntriesDiv>

          <Styled.PageInput
            type="number"
            min={1}
            max={pageCount}
            value={inputPage}
            onChange={(e) => {
              const rawValue = e.target.value
              setInputPage(rawValue) // Allow typing

              // Validate and debounce
              const page = rawValue ? Math.min(Math.max(1, Number(rawValue)), pageCount) - 1 : 0
              gotoPage(page)
            }}
          />

          <Styled.PageSpan>of {pageCount}</Styled.PageSpan>

          <Styled.ShowEntriesDiv>
            <Styled.IconContainer onClick={nextPage} disabled={!canNextPage}>
              ▶
            </Styled.IconContainer>

            <Styled.IconContainer onClick={() => gotoPage(pageCount - 1)} disabled={!canNextPage}>
              ⏭
            </Styled.IconContainer>
          </Styled.ShowEntriesDiv>

          <Styled.ShowEntriesDiv>
            <Styled.ShowText>Per page:</Styled.ShowText>
            <Styled.SelectDiv
              value={pageSize}
              onChange={(e) => {
                const newSize = Number(e.target.value)
                setPageSize(newSize)
                gotoPage(0) // <-- Reset to first page to prevent inconsistency
              }}
            >
              {[10, 20, 30, 40, 50].map((size) => (
                <Styled.SelectOption key={size} value={size}>
                  {size}
                </Styled.SelectOption>
              ))}
            </Styled.SelectDiv>
          </Styled.ShowEntriesDiv>
        </Styled.ShowEntriesDiv>
      </Styled.Pagination>

      <Styled.TableOuterContainer>
        <Styled.TableContainer {...getTableProps()} noLink={noLink} minWidth={minWidth}>
          {!hideHeader && (
            <Styled.TableHeader>
              {headerGroups.map((headerGroup: any, idx: number) => (
                <Styled.TableRow {...headerGroup.getHeaderGroupProps()} noLink={true} key={idx}>
                  {headerGroup.headers.map((column: any, index: number) => (
                    <Styled.TableHeading noBorder={noBorder} padding={padding} {...column.getHeaderProps()} key={index}>
                      {column.render('Header')}
                    </Styled.TableHeading>
                  ))}
                </Styled.TableRow>
              ))}
            </Styled.TableHeader>
          )}

          <Styled.TableBody {...getTableBodyProps()}>
            {rows.length > 0 ? (
              rows.map((row: any, index: number) => {
                prepareRow(row)
                return (
                  <Fragment key={index}>
                    <Styled.TableRow
                      noBorder={noBorder}
                      key={index}
                      {...row.getRowProps()}
                      onClick={() => {
                        if (onRowClick) {
                          onRowClick(row.original)
                        }
                      }}
                    >
                      {row.cells.map((cell: any, idx: number) => (
                        <Styled.TableData
                          noBorder={noBorder}
                          padding={row.original.padding && idx === 0 ? row.original.padding : padding}
                          {...cell.getCellProps()}
                          key={idx}
                        >
                          {cell.render('Cell')}
                        </Styled.TableData>
                      ))}
                    </Styled.TableRow>
                  </Fragment>
                )
              })
            ) : globalFilter ? (
              <Styled.LoaderContainer>
                <td>
                  <Styled.LoaderContent>No Results</Styled.LoaderContent>
                </td>
              </Styled.LoaderContainer>
            ) : loading ? (
              [1, 2, 3, 4, 5, 6].map((idx: number) => (
                <Styled.TableRow key={idx} noLink>
                  {headerGroups[0].headers.map((headerGroup: any, key: number) => (
                    <Styled.TableData key={key}>
                      <SharedStyled.SkeletonLoader key={key}>
                        <div className="skeleton"></div>
                      </SharedStyled.SkeletonLoader>
                    </Styled.TableData>
                  ))}
                </Styled.TableRow>
              ))
            ) : (
              <Styled.LoaderContainer>
                <td>
                  <Styled.LoaderContent>No Results</Styled.LoaderContent>
                </td>
              </Styled.LoaderContainer>
            )}
          </Styled.TableBody>
        </Styled.TableContainer>
      </Styled.TableOuterContainer>
    </>
  )
})
