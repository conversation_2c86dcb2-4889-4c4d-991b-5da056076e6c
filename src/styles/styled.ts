import styled, { css, keyframes, ThemeProps } from 'styled-components'
import { Field } from 'formik'
import { fontSizes, screenSizes, gapSizes, Theme, colors, Themes, lineHeights } from './theme'
import { rgba } from 'polished'
import { Nue } from '../shared/helpers/constants'
import { SettingModalContainer, SettingModalHeaderContainer } from '../modules/units/components/newUnitModal/style'

export const pageContentWidth = screenSizes.XL

export const PageContainer = styled.div<any>`
  display: flex;
  flex-direction: column;
  align-self: center;
  width: calc(100% - 2 * var(--pageMargin));
  max-width: ${pageContentWidth}px;
  margin: 0 auto;
  min-height: auto;
  padding: ${(props) => (!props.noPadding ? `${gapSizes.XXL} 0 ` : '0')};
  padding-bottom: 100px;
  transition: all 300ms ease-in-out;
`

export const whiteColor = css`
  color: ${({ theme }) => theme.white};
`

export const grayColor = css`
  color: ${({ theme }) => theme.gray};
`

export const semiBoldStyle = css`
  font-family: ${Nue.medium};
  font-weight: 600;
`

export const boldStyle = css`
  font-family: ${Nue.bold};
  font-weight: 800;
`

export const fieldHeight = '48px'

export const fieldBorder = css`
  border: none;
  border-bottom: 1px solid ${({ theme }) => theme.gray};
  border-radius: ${(props) => props.theme.radius};
`

export const colorChangeDelay = '1s'

export const Container = styled.div`
  max-width: 1280px;
  margin: auto;
  width: 100%;
`

export const CenterContainer = styled.div`
  text-align: center;
`

export const StyledContent = styled.div`
  > div {
    display: flex;
    justify-content: space-between;
    margin: 0 20px;
  }
`

// TODO: Might need width: fit-content;
export const StyledH1 = styled.h1`
  color: ${(props: any) => (props.color ? props.color : props.theme.white)};
  /* font-family: 'NunitoSansBold'; */
`

export const StyledH2 = styled.h2`
  color: ${(props: any) => (props.color ? props.color : props.theme.white)};
  text-align: start;
  font-size: 1.4em;
  font-weight: 100;
  margin: 30px 0;
  padding-top: 20px;
`

export const PointedStyledH2 = styled<any>(StyledH2)`
  cursor: pointer;
  margin: 0;
  float: left;
  width: fit-content;
`

export const inputStyleDynamicSize = css<any>`
  outline: none;
  font-size: ${fontSizes.XS} !important;
  line-height: ${lineHeights.XS} !important;
  padding: 12px;
  border: none;
  border-radius: 4px !important;
  color: ${(props) => props.theme.white};
  caret-color: ${(props) => props.theme.white};
  :-webkit-autofill {
    -webkit-border: ${(props) => props.hasErrors && `2px solid ${colors.errorRed}`} !important;
    -webkit-text-fill-color: ${(props) => (props.hasErrors ? colors.errorRed : props.theme.white)};
    -webkit-box-shadow: 0 0 0px 1000px ${(props) => props.theme.input.basic} inset !important;
    border: 4px solid ${(props) => props.theme.input.basic} !important;
    &:hover,
    &:focus {
      -webkit-text-fill-color: ${(props) => (props.hasErrors ? colors.errorRed : props.theme.white)};
      -webkit-box-shadow: 0 0 0px 1000px ${(props) => props.theme.input.basic} inset !important;
      border: ${(props) => (props.hasErrors ? `2px solid ${colors.errorRed}` : `1px solid ${colors.green}`)} !important;
    }
  }
  svg {
    path {
      fill: ${(props) => props.theme.listText};
    }
    :hover {
      path {
        fill: ${colors.green};
      }
    }
  }
  :hover,
  :focus {
    background-color: ${(props) => props.theme.input.basic} !important;
    box-shadow: 0 1px 0 0 ${(props) => props.theme.accent}, 0 -1px 0 0 ${(props) => props.theme.accent},
      -1px 0 0 0 ${(props) => props.theme.accent}, 1px 0 0 0 ${(props) => props.theme.accent},
      0 0 0 1px ${(props) => props.theme.accent};
  }
  :focus {
    svg {
      path {
        fill: ${(props) => props.theme.white};
      }
    }
  }
  &::placeholder {
    color: ${(props) => props.theme.listDesc};
    font-size: 16px;
  }
  &:disabled {
    background-color: ${(props) => props.theme.input.disabled} !important;
    // color: rgba(92,106,115, 0.4)} !important;
    &::placeholder {
      color: ${(props) => props.theme.highlight};
    }
    :hover,
    :focus {
      // background-color: ${(props) => props.theme.input.disabled} !important;
      box-shadow: none;
    }
  }
`

export const commonInputStyle = css<any>`
  ${inputStyleDynamicSize}
  height: ${fieldHeight} !important;
  width: 100% !important;
`

export const CommonInput = styled.input<any>`
  ${commonInputStyle}
  background-color: ${(props) => (props.notEmpty === 'true' ? props.theme.input.basic : props.theme.input.default)};
  border: ${(props) => props.hasErrors && `2px solid ${colors.errorRed}`} !important;
  color: ${(props) => props.hasErrors && colors.errorRed} !important;
  box-shadow: ${(props) => props.hasErrors && 'none'} !important;
  &::placeholder {
    color: ${(props) => props.hasErrors && colors.errorRed} !important;
  }
`

export const InputGroup = styled.div<any>`
  flex: 1;
  display: flex;
  flex-direction: row;
  > div {
    flex: 1;
    display: flex;
    flex-direction: column;
    + div {
      margin-left: 10px;
    }
  }
  @media (max-width: ${screenSizes.M}px) {
    flex-direction: ${(props) => !props.disableMedia && 'column'};
    > div + div {
      margin-left: ${(props) => !props.disableMedia && 0};
    }
  }
`

export const HorizontalLine = styled.hr`
  height: 1px;
  min-width: 100%;
  width: auto;
  background-color: ${({ theme }) => theme.highlight};
`

export const TextInput = styled.textarea`
  ${fieldBorder};
  margin-top: 4px;
  background: #ffffff;
  box-sizing: border-box;
  resize: none;
`

export const Header2 = styled.div`
  font-weight: 300;
  font-size: 24px;
  a {
    color: ${(props) => props.theme.white};
  }
`

export const Header3 = styled.div`
  font-weight: 300;
  font-size: 20px;
  a {
    color: ${(props) => props.theme.white};
  }
`

export const CenteringContainer = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;
`

export const StyledP = styled.p`
  color: ${({ theme }) => theme.white};
`

export const boldText = css`
  font-weight: bold;
  font-family: ${Nue.bold};
`

export const subduedText = css`
  opacity: 0.5;
  font-size: ${fontSizes.XS};
`

export interface CellProps {
  column: number
  row: number
}

export const cell = css<CellProps>`
  grid-column: ${(props) => props.column};
  grid-row: ${(props) => props.row};
`

export const Cell = styled.div`
  ${cell}
`

interface IFlexRow {
  alignItems?: 'stretch' | 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'start'
  justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly'
  padding?: string
  margin?: string
  gap?: string
  flexWrap?: string
  whiteSpace?: string
  overflowX?: string
  flexDirection?: 'row' | 'row-reverse' | 'column'
  width?: string
}

export const FlexRow = styled.div<IFlexRow>`
  width: ${(props) => (props.width ? props.width : '100%')};
  display: flex;
  flex-direction: ${(props) => (props.flexDirection ? props.flexDirection : 'row')};
  align-items: ${(props) => (props.alignItems ? props.alignItems : 'center')};
  justify-content: ${(props) => (props.justifyContent ? props.justifyContent : '')};
  margin: ${(props) => (props.margin ? props.margin : '')};
  padding: ${(props) => (props.padding ? props.padding : '')};
  gap: ${(props) => (props.gap ? props.gap : '10px')};
  flex-wrap: ${(props) => (props.flexWrap ? props.flexWrap : '')};
  white-space: ${(props) => props.whiteSpace};
  overflow-x: ${(props) => props.overflowX};
`

interface IFlexCol {
  alignItems?: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline'
  justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly'
  padding?: string
  margin?: string
  gap?: string
  flexWrap?: string
  whiteSpace?: string
  overflowX?: string
  flex?: string
  width?: string
}

export const FlexCol = styled.div<IFlexCol>`
  width: ${(props) => (props.width ? props.width : '100%')};
  display: flex;
  flex-direction: column;
  justify-content: ${(props) => (props.justifyContent ? props.justifyContent : 'center')};
  align-items: ${(props) => (props.alignItems ? props.alignItems : 'flex-start')};
  flex: ${(props) => props.flex};
  margin: ${(props) => props.margin};
  padding: ${(props) => (props.padding ? props.padding : '')};
  gap: ${(props) => (props.gap ? props.gap : ' ')};
  flex: ${(props) => props.flex};
`

export const fullScreenOvershadow = css`
  z-index: 999999;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2) !important;
`

export const fieldLabel = css`
  text-align: start;
  color: ${({ theme }) => theme.label};
  line-height: 22px;
  font-size: 0.9em;
`

export const fieldSpan = css`
  margin: 20px 0;
  display: block;
  ${whiteColor}
`

export const LegendTextInput = styled<any>(TextInput)`
  width: 100%;
  height: 100px;
`

export const SmallHeader = styled.div`
  font-size: ${fontSizes.M};
  ${whiteColor}
`
export const Divider = css`
  margin-top: 20px;
  margin-bottom: 20px;
`

export const DarkDivider = styled.div`
  ${Divider}
  border-bottom: 1px solid #2B3B44;
`
export const LightDivider = styled.div<any>`
  ${Divider}
  border-bottom: 1px solid ${colors.grayDivider};
  max-width: ${(props) => (props.stretch ? '' : '600px')};
`

export const StyledDetails = styled.div`
  display: flex;
`

export const DetailsH4 = css`
  font-size: 20px;
  line-height: 28px;
`

export const DetailsSpan = css`
  font-size: 16px;
  line-height: 24px;
`

export const DetailsLabel = styled.div`
  ${fieldLabel}
  margin: 0 0 4px 0;
  font-size: ${fontSizes.XS};
  word-break: break-all;
`

export const DetailsMaxWidth = css`
  max-width: 600px;
`

export const OrgDetailsWrapper = styled.div`
  max-width: 628px;
  color: ${({ theme }) => theme.white};
  h4 {
    ${DetailsH4}
    padding-bottom: 24px;
    padding-top: 20px;
  }
  span {
    ${DetailsSpan}
    margin: 0;
  }
  .greyed {
    color: #9ca6ad;
  }
  .idCopy {
    display: flex;
    align-items: center;
    justify-content: space-between;
    svg {
      margin: 0;
    }
  }
  div {
    svg {
      cursor: pointer;
      margin: 0 0 6px 10px;
    }
  }
`

export const TextAreaInput = styled.textarea<any>`
  ${inputStyleDynamicSize};
  height: ${(props) => (props.height ? props.height : '128px')};
  width: 100%;
  margin: 0;
  background-color: ${(props) => (props.notEmpty === 'true' ? props.theme.input.basic : props.theme.input.default)};
  border: ${(props) => props.hasErrors && `2px solid ${colors.errorRed}`};
  color: ${(props) => props.hasErrors && colors.errorRed} !important;
  box-shadow: ${(props) => props.hasErrors && 'none'} !important;
  &::placeholder {
    font-size: ${fontSizes.XS};
    color: ${(props) => props.hasErrors && colors.errorRed} !important;
  }
`

export const FieldNote = styled.p<any>`
  color: ${(props) => (props.hasErrors ? colors.errorRed : '#9CA6AD')};
  font-size: 14px;
  line-height: 19px !important;
  margin: 2px 0 0 0 !important;
`

export const StyledCertificateImage = styled.img`
  width: 60px;
  border: 2px solid ${(props) => props.theme.accent};
`

export const StyledPaymentMethod = css`
  background: ${(props) => (props.theme.selected !== Themes.LIGHT ? props.theme.primary : props.theme.panelBack)};
  border-radius: 4px;
  margin-bottom: 20px;
  div {
    display: flex;
    align-items: center;
  }
  span {
    margin: 0;
    font-size: ${fontSizes.XXS};
  }
`

export const crossWithHoverCss = css<any>`
  width: 32px !important;
  height: 32px;
  border-radius: 4px;
  justify-content: center;
  cursor: pointer;
  &:hover {
    background: ${(props) => props.theme.buttonHover};
    path {
      fill: ${(props) => (props.theme.selected !== Themes.LIGHT ? props.theme.white : colors.white)};
    }
  }
  &:active {
    background: ${(props) => props.theme.buttonActive};
  }
`

export const CrossWithHover = styled<any>(FlexRow)`
  ${crossWithHoverCss}
`

export const RadioWrap = styled<any>(FlexRow)`
  label {
    padding-left: 28px;
    font-size: 14px;
    line-height: 19px;
  }
  .multi {
    margin-right: 48px !important;
    :last-child {
      margin-right: 0 !important;
    }
  }
  @media (max-width: ${screenSizes.S}px) {
    flex-direction: column;
    align-items: flex-start;
  }
`

export const FieldPanel = styled.div<any>`
  width: 100%;
  background-color: ${(props) => props.theme.selectorBack};
  border-radius: 8px;
  padding: 24px;
  margin-bottom: ${(props) => (!!props.isLast ? '' : '25px')};
  .cross {
    cursor: pointer;
    path {
      fill: ${(props) => props.theme.whiteIcon};
      fill-opacity: 1;
    }
  }
`

export const ListLabel = styled<any>(DetailsLabel)`
  font-weight: bold;
  font-family: ${Nue.medium};
  color: ${(props) => (props.error === 'true' ? colors.errorRed : props.theme.white)};
  margin: 4px 0 4px 0;
`

export const ListValue = styled.span<any>`
  font-size: 16px !important;
  line-height: 24px !important;
  color: ${(props) => (props.error === 'true' ? colors.errorRed : '')};
`

interface I_ContentContainer {
  padding?: string
  flexDirection?: string
}

export const SettingModalContentContainer = styled.div<I_ContentContainer>`
  width: 100%;
  display: flex;
  flex-direction: ${(props) => (props.flexDirection ? props.flexDirection : 'row')};
  justify-content: center;
  .form {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
  }
  padding: ${(props) => (props.padding ? props.padding : '12px')};

  @media (min-width: ${screenSizes.S}px) {
    padding: ${(props) => (props.padding ? props.padding : '24px')};
  }

  .paySchedule {
    flex-direction: column;
  }

  .btnCont {
    flex-wrap: nowrap;
    @media (max-width: ${screenSizes.XS}px) {
      flex-wrap: wrap;
    }
  }

  &.mobile-container {
    margin-top: 20px;
    padding: 12px 0;
  }
`

interface I_Content {
  width?: string
  maxWidth?: string
  disableBoxShadow?: boolean
  noPadding?: boolean
  margin?: string
  alignItems?: string
  maxHeight?: string
  overflow?: string
  gap?: string
  textAlign?: string
  borderRadius?: string
  padding?: string
  fontFamily?: string
}

export const Content = styled.div<I_Content>`
  display: flex;
  flex-direction: column;
  font-family: ${(props) => props.fontFamily};
  /* gap: 8px; */
  gap: ${(props) => props.gap};
  justify-content: flex-start;
  align-items: ${(props) => props.alignItems ?? 'center'};
  max-width: ${(props) => props.maxWidth};
  width: ${(props) => (props.width ? props.width : 'fit-content')};
  padding: ${(props) => (props.noPadding ? 'none' : props.padding ?? '30px')};
  max-height: ${(props) => props.maxHeight};
  text-align: ${(props) => props.textAlign};
  overflow: ${(props) => props.overflow ?? 'auto'};
  background: ${colors.white};
  border-radius: ${(props) => props.borderRadius ?? '0px'};
  box-shadow: ${(props) => (props.disableBoxShadow ? 'none' : '1px 0px 24px -9px rgba(0, 0, 0, 0.69)')};
  -webkit-box-shadow: ${(props) => (props.disableBoxShadow ? 'none' : '1px 0px 24px -9px rgba(0, 0, 0, 0.69)')};
  -moz-box-shadow: ${(props) => (props.disableBoxShadow ? 'none' : '1px 0px 24px -9px rgba(0, 0, 0, 0.69)')};
  transition: 0.3s;
  margin: ${(props) => props.margin};

  &.fix-height {
    overflow-y: scroll;
    max-height: 100vh;

    @media (max-width: ${screenSizes.XXS}px) {
      padding: 16px 8px !important;
    }

    &::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */

    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
`
interface I_Header {
  textAlign?: string
  width?: string
  display?: string
  justifyContent?: string
  alignItems?: string
  flexDirection?: string
  padding?: string
  margin?: string
  fontWeight?: string
  textTransform?: string
  pointer?: string
}

export const ContentHeader = styled.h4<I_Header>`
  color: ${colors.darkGrey};
  width: ${(props) => (props.width ? props.width : '100%')};
  text-align: ${(props) => (props.textAlign ? props.textAlign : 'center')};
  display: ${(props) => (props.display ? props.display : 'block')};
  justify-content: ${(props) => (props.justifyContent ? props.justifyContent : '')};
  align-items: ${(props) => (props.alignItems ? props.alignItems : '')};
  flex-direction: ${(props) => (props.flexDirection ? props.flexDirection : '')};
  padding: ${(props) => (props.padding ? props.padding : '')};
  margin: ${(props) => props.margin};
  width: 100%;
  font-family: ${Nue.regular};
  font-weight: ${(props) => (props.fontWeight ? props.fontWeight : '')};
  text-transform: ${(props) => props.textTransform};
  cursor: ${(props) => props.pointer};
`

interface I_HorizontalDivider {
  height?: string
  margin?: string
  bg?: string
}

export const HorizontalDivider = styled.hr<I_HorizontalDivider>`
  width: 100%;
  height: ${(props) => (props.height ? props.height : '1px')};
  color: rgba(0, 0, 0, 0.1);
  background: ${(props) => (props.bg ? props.bg : 'rgba(0, 0, 0, 0.1)')};
  margin: ${(props) => (props.margin ? props.margin : 'auto')};
`

export const ButtonContainer = styled.div<any>`
  width: ${(props) => (props.width ? props.width : '100%')};
  display: flex;
  justify-content: ${(props) => (props.justifyContent ? props.justifyContent : 'space-between')};
  gap: 10px;
  margin-top: ${(props) => props.marginTop};
  margin: ${(props) => (props.margin ? props.margin : '')};
  padding: ${(props) => (props.padding ? props.padding : '')};

  &.mobile {
    @media (max-width: 425px) {
      flex-wrap: wrap;
      flex-direction: column-reverse;
    }
  }
`

interface I_Button {
  color?: string
  bgColor?: string
  maxWidth?: string
  minWidth?: string
  width?: string
  boxShadow?: string
  maxHeight?: string
  height?: string
  mediaHeight?: string
  mediaFontSize?: string
  marginTop?: string
  borderRadius?: string
  alignSelf?: string
  outline?: string
  whiteSpace?: string
  small?: boolean
}

export const Button = styled.button<I_Button>`
  max-width: ${(props) => props.maxWidth};
  min-width: ${(props) => props.minWidth};
  max-height: ${(props) => props.maxHeight};
  margin-top: ${(props) => props.marginTop};
  outline: ${(props) => props.outline};
  width: ${(props) => (props.width ? props.width : '100%')};
  height: ${(props) => (props.height ? props.height : '40px')};
  font-size: 12px;
  font-weight: 300px;
  cursor: pointer;
  border: none;
  white-space: ${(props) => props?.whiteSpace};
  border-radius: ${(props) => (props.borderRadius ? props.borderRadius : '8px')};
  color: ${(props) => (props.color ? props.color : colors.white)};
  background-color: ${(props) => (props.bgColor ? props.bgColor : colors.lightBlue)};
  box-shadow: ${(props) => props.boxShadow};
  transition: all 0.1s linear;
  /* &:hover {
    transform: scale(1.01);
  } */
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  @media (min-width: ${screenSizes.M}px) {
    font-size: ${(props) => (props.mediaFontSize ? props.mediaFontSize : '16px')};
    font-weight: 400px;
    height: ${(props) => (props.mediaHeight ? props.mediaHeight : '45px')};
  }
  align-self: ${(props) => props.alignSelf};

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  &.delete {
    background-color: ${colors.lightRed2};
  }
`

export const RoundButton = styled.div`
  display: flex;
  width: 32px;
  height: 32px;
  padding: 8px;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  background: ${rgba(colors.gray, 0.1)};
  cursor: pointer;

  &:hover {
    filter: brightness(75%);
  }

  img {
    width: 15px;
  }

  @media (min-width: ${screenSizes.XS}px) {
    padding: 12px;
    width: 40px;
    height: 40px;
  }
`

export const Loader = styled.div<any>`
  width: ${(props) => (props.width ? props.width : '15px')};
  height: ${(props) => (props.height ? props.height : '15px')};
  border: 3px solid ${(props) => (props.color ? props.color : colors.white)};
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;

  @keyframes rotation {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @media (min-width: ${screenSizes.M}px) {
    width: ${(props) => (props.width ? props.width : '20px')};
    height: ${(props) => (props.height ? props.height : '20px')};
  }
`

export const TwoInputDiv = styled.div`
  display: flex;
  margin-top: 2px;
  width: 100%;
  justify-content: space-between;
  gap: 10px;
  align-items: flex-start;
  @media (max-width: ${screenSizes.M}px) {
    flex-wrap: wrap;
  }
`

interface I_DropDownContainer {
  border?: string
  height?: string
  marginTop?: string
  bgColor?: string
  color?: string
}

export const DropDownContainer = styled.div<any>`
  position: relative;
  border: ${(props) =>
    props.focus
      ? `1px solid ${colors.lightBlue1}`
      : props.error
      ? `1px solid ${colors.lightRed1}`
      : `1px solid ${colors.darkGrey}`};
  box-shadow: ${(props) => (props.focus ? `${colors.lightBlue} 0px 0px 5px 0px` : `none`)};
  box-sizing: border-box;
  border-radius: 8px;
  max-width: ${(props) => (props.maxWidth ? props.maxWidth : '400px')};
  width: 100%;
  height: ${(props) => (props.height ? props.height : '52px')};
  padding: 20px 16px 8px;
  margin-top: ${(props) => props.marginTop};
  background: ${(props) =>
    props.disabled ? `${colors.grey2}` : props.error ? `${colors.lightRed}` : `${colors.white}`};
  color: ${(props) => (props.color ? props.color : `${colors.white}`)};
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
`
export const DropDownLabel = styled.label`
  color: ${colors.darkGrey};
  position: absolute;
  font-size: 12px;
  top: 5px;
  left: 17px;
`
export const DropDownContent = styled.div<any>`
  font-family: ${(props) => (props.active ? Nue.medium : ``)};
  color: ${colors.darkGrey};
  position: absolute;
  font-size: 16px;
  font-weight: 500;
  top: ${(props) => (props.active ? `20px` : `15px`)};
  left: 17px;
`

export const DropdownIconDiv = styled.div`
  position: absolute;
  top: 15px;
  right: 10px;
  svg {
    width: 20px;
    height: 20px;
    path {
      stroke: ${colors.darkGrey};
    }
  }
`

interface I_SearchInput {
  border?: string
  height?: string
  width?: string
  bgColor?: string
  color?: string
  padding?: string
  borderRadius?: string
  fontSize?: string
}

export const SearchInput = styled.input<I_SearchInput>`
  width: ${(props) => (props.width ? props.width : '600px')};
  height: ${(props) => (props.height ? props.height : '30px')};
  background: ${(props) => (props.bgColor ? props.bgColor : `${colors.darkGrey}`)};
  color: ${(props) => (props.color ? props.color : `${colors.white}`)};
  outline: none;
  padding: ${(props) => props.padding};
  border-radius: ${(props) => props.borderRadius};
  font-size: ${(props) => props.fontSize};
  border: ${(props) => (props.border ? props.border : `2px solid ${colors.white}`)};
  /* border: none; */
  @media (min-width: ${screenSizes.M}px) {
    height: ${(props) => (props.height ? props.height : '36px')};
  }
`

export const IconDiv = styled.div<any>`
  svg {
    width: 15px;
    height: 15px;
  }
`

interface I_DropDownContentContainer {
  border?: string
  height?: string
  marginTop?: string
  bgColor?: string
  color?: string
  visibility: boolean
}

export const DropDownContentContainer = styled.div<I_DropDownContentContainer>`
  display: ${(props) => (props.visibility ? 'block' : 'none')};
  position: absolute;
  border: ${(props) => (props.border ? props.border : `2px solid ${colors.white}`)};
  width: 100%;
  height: ${(props) => (props.height ? props.height : '120px')};
  top: 60px;
  left: 0px;
  border-radius: 8px;
  overflow-y: auto;
  background: ${(props) => (props.bgColor ? props.bgColor : `${colors.darkGrey}`)};
  z-index: 2;
`

interface I_DropDownItem {
  bgActive?: string
  bgNotActive?: string
  colorActive?: string
  colorNotActive?: string
  bgHover?: string
  colorHover?: string
  active: boolean
  borderBottom?: string
}

export const DropDownItem = styled.div<I_DropDownItem>`
  padding: 8px;
  /* border-radius: 8px; */
  width: 100%;
  background: ${(props) => (props.active ? `${props.bgActive}` : `${props.bgNotActive}`)};
  color: ${(props) => (props.active ? `${props.colorActive}` : `${props.colorNotActive}`)};
  border-bottom: ${(props) => props.borderBottom};
  :hover {
    background: ${(props) => props.bgHover};
    color: ${(props) => props.colorHover};
  }
  :last-child {
    border-bottom: none;
  }
`

export const InputLabelDiv = styled.div<any>`
  width: 100%;
  display: flex;
  flex-direction: ${(props) => (props.flexDirection ? props.flexDirection : 'column')};
  align-items: flex-start;
  gap: ${(props) => props.gap};
  margin-top: 10px;
`

export const InputLabelDivRow = styled.div<any>`
  width: 100%;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-top: ${(props) => props.marginTop};
  flex-wrap: wrap;
  input[type='checkbox'] {
    cursor: pointer;
  }
`

interface FlexBoxProps {
  width?: string
  justifyContent?: string
  alignItems?: string
  textAlign?: string
  flexDirection?: string
  gap?: string
  marginTop?: string
  border?: string
  margin?: string
  padding?: string
  flexWrap?: string
  maxWidth?: string
  height?: string
  heightM?: string
  maxHeight?: string
  overflow?: string
  mediaMaxWidth?: string
  wrap?: string
  column?: string
  alignItemsM?: string
  onClick?: React.MouseEventHandler<HTMLDivElement>
}

export const FlexBox = styled.div<FlexBoxProps>`
  width: ${(props) => props.width};
  display: flex;
  justify-content: ${(props) => props.justifyContent};
  align-items: ${(props) => props.alignItems};
  text-align: ${(props) => props.textAlign};
  flex-direction: ${(props) => props.flexDirection};
  gap: ${(props) => props.gap};
  margin-top: ${(props) => props.marginTop};
  margin: ${(props) => props.margin};
  padding: ${(props) => props.padding};
  flex-wrap: ${(props) => props.flexWrap};
  max-width: ${(props) => props.maxWidth};
  border: ${(props) => props.border};
  height: ${(props) => props.height};
  max-height: ${(props) => props.maxHeight};
  overflow: ${(props) => props.overflow};
  padding: ${(props) => props.padding};
  cursor: ${(props) => (props?.onClick ? 'pointer' : '')};
  @media (max-width: ${screenSizes.M}px) {
    flex-wrap: ${(props) => props.wrap ?? props.flexWrap};
    max-width: ${(props) => props.mediaMaxWidth ?? props.maxWidth};
    height: ${(props) => props.heightM};
    flex-direction: ${(props) => props.column};
    align-items: ${(props) => props.alignItemsM};
  }
`

export const Label = styled.label<any>`
  width: fit-content;
  font-family: ${(props) => props.bold && Nue.bold};
  font-weight: 400;
  font-size: 13px;
  line-height: 12px;
  text-transform: capitalize;
  color: ${colors.darkGrey};
  align-self: ${(props) => props.align};
  margin-top: ${(props) => props.marginTop};
  font-family: ${Nue.regular};
`

export const InputField = styled(Field)<any>`
  border: 2px solid ${colors.darkGrey};
  box-sizing: border-box;
  border-radius: 8px;
  width: ${(props) => (props.width ? props.width : '100%')};
  height: ${(props) => (props.height ? props.height : '48px')};
  padding: 12px 18px;
  &.textArea {
    height: 100px;
    resize: none;
  }
  margin-top: ${(props) => props.marginTop};
  background: ${colors.white};
  color: ${colors.darkGrey};
  cursor: ${(props) => props.cursor};
`
export const CardSkeleton = styled.span<any>`
  width: 400px;
  height: ${(props) => props.height};
  display: block;
  margin: auto;
  position: relative;
  background: #fff;
  box-sizing: border-box;

  &&::after {
    content: '';
    width: calc(100% - 30px);
    height: calc(100% - 15px);
    top: 15px;
    left: 15px;
    position: absolute;
    background-image: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5) 50%, transparent 100%),
      linear-gradient(#ddd 100px, transparent 0), linear-gradient(#ddd 16px, transparent 0),
      linear-gradient(#ddd 50px, transparent 0);
    background-repeat: no-repeat;
    background-size: 75px 175px, 100% 100px, 100% 16px, 100% 30px;
    background-position: -185px 0, center 0, center 115px, center 142px;
    box-sizing: border-box;
    animation: animloader 1s linear infinite;
  }

  @keyframes animloader {
    to {
      background-position: 185px 0, center 0, center 115px, center 142px;
    }
  }
`
export const Skeleton = styled.div<any>`
  @keyframes placeHolderShimmer {
    0% {
      background-position: -468px 0;
    }
    100% {
      background-position: 468px 0;
    }
  }
  animation-duration: 1s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: placeHolderShimmer;
  animation-timing-function: linear;
  background: #f6f7f8;
  background: linear-gradient(to right, #cccccc 8%, #b4b4b4 18%, #cccccc 33%);
  background-size: 1000px 104px;
  height: ${(props: any) => (props.custHeight ? props.custHeight : '338px')};
  width: ${(props: any) => (props.custWidth ? props.custWidth : 'auto')};
  margin: ${(props: any) => (props.custMargin ? props.custMargin : '')};
  position: relative;
  overflow: hidden;
  border-radius: 6px;
  margin-top: ${(props: any) => (props.custMarginTop ? props.custMarginTop : '')};
  @media (max-width: ${screenSizes.M}px) {
    margin: ${(props: any) => (props.isMuscleReqBar ? '0 0 5px 0' : '')};
  }
`

const skeletonAnimation = keyframes`
  from {
    left: -150px;
  }
  to {
    left: 100%;
  }
`

export const SkeletonLoader = styled.div<{ width?: number; height?: number; isPercent?: boolean; margin?: string }>`
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  width: inherit;

  .skeleton {
    background: #e1e1e1;
    width: ${(props) => (props.isPercent ? `${props.width}%` : props.width ? `${props.width}px` : 'inherit')};
    height: ${(props) => (props.height ? `${props.height}px` : '20px')};
    border-radius: 4px;
    margin: ${(props) => props.margin};
    position: relative;
    overflow: hidden;
  }

  .skeleton::before {
    content: '';
    display: block;
    position: absolute;
    left: -150px;
    top: 0;
    height: 100%;
    width: 150px;
    background: linear-gradient(to right, transparent 0%, #e8e8e8 50%, transparent 100%);
    animation: ${skeletonAnimation} 1s cubic-bezier(0.4, 0, 0.2, 1) infinite;
  }
`
export const TextArea = styled(Field)<any>`
  width: ${(props) => (props.width ? props.width : '100%')};
  height: ${(props) => (props.height ? props.height : '48px')};
  margin-top: ${(props) => props.marginTop};
  cursor: pointer;
  resize: vertical;
  outline: none;
  border: 1px solid ${colors.lightGray};
  border-radius: 8px;
  padding: 12px 18px;
  max-height: 400px;
  min-height: 100px;
  :focus {
    border: 1px solid ${colors.lightBlue1};
    box-shadow: ${colors.lightBlue} 0px 0px 5px 0px;
  }
  margin-top: ${(props) => props.marginTop};
`

export const LabelDiv = styled.label<any>`
  font-size: 14px;
  font-weight: 500;
  color: ${colors.darkGrey};
  margin-top: ${(props) => props.marginTop};
  margin: ${(props) => props.margin};
  text-align: ${(props) => props.textAlign};
  width: ${(props) => props.width};
  cursor: ${(props) => props.cursor};
  @media (min-width: ${screenSizes.M}px) {
    font-size: 16px;
  }
`

interface I_Text {
  color?: string
  fontWeight?: string
  fontSize?: string
  variant?: 'link'
  margin?: string
  padding?: string
  width?: string
  whiteSpace?: string
  textAlign?: string
  textTransform?: string
  textDecoration?: string
  familyTypeMedium?: boolean
  backgroundColor?: string
  borderRadius?: string
}
export const Text = styled.span<I_Text>`
  color: ${(props) => props.color ?? 'black'};
  background-color: ${(props) => props.backgroundColor};
  font-family: ${(props) => (props.familyTypeMedium ? `${Nue.medium}` : `${Nue.regular}`)};
  font-weight: ${(props) => props.fontWeight ?? 'normal'};
  font-size: ${(props) => props.fontSize ?? '14px'};
  margin: ${(props) => props.margin ?? '0'};
  padding: ${(props) => props.padding ?? '0'};
  width: ${(props) => props.width ?? 'auto'};
  border-radius: ${(props) => props.borderRadius};
  white-space: ${(props) => props.whiteSpace ?? ''};
  text-align: ${(props) => props.textAlign ?? ''};
  text-transform: ${(props) => props.textTransform};
  text-decoration: ${(props) => props.textDecoration};
  cursor: ${(props) => (props.variant === 'link' ? 'pointer' : '')};
  :hover {
    text-decoration: ${(props) => (props.variant === 'link' ? 'underline' : 'none')};
  }
`

export const ValueInputLabel = styled.label<any>`
  width: fit-content;
  font-family: ${(props) => props.bold && Nue.bold};
  font-weight: ${(props) => props.fontWeight ?? '400'};
  font-size: 13px;
  line-height: 12px;
  text-transform: capitalize;
  color: ${colors.black};
  align-self: ${(props) => props.align};
  margin-top: ${(props) => props.marginTop};
`

export const ValueInput = styled(Field)<any>`
  height: ${(props) => (props.height ? props.height : '100%')};
  padding: 6.5px 10px;
  outline: none;
  color: ${colors.lightGrey4};
  border: 1px solid ${colors.lightGrey6};
  width: ${(props) => props.width};
  ::-webkit-outer-spin-button,
  ::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  :focus {
    border: 1px solid ${colors.lightBlue1};
    box-shadow: ${colors.lightBlue} 0px 0px 5px 0px;
    /* background-color: ${colors.white}; */
  }
  border-radius: ${(props) => props.borderRadius};
  min-width: ${(props) => props.minWidth};
  font-family: ${Nue.regular};
`

export const UnitDiv = styled.div<any>`
  height: ${(props) => (props.disableHeight ? 'auto' : '100%')};
  padding: 6px 12px;
  background: ${(props) => (props.error ? `${colors.lightRed}` : `${colors.lightGrey3}`)};
  /* background-color: ${colors.lightGrey3}; */
  border-radius: 0px 4px 4px 0px;
  border: 1px solid ${(props) => (props.error ? `${colors.lightRed1}` : `${colors.lightGrey6}`)};
  font-size: 14px;
  color: ${colors.lightGrey4};
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-width: 50px;
  font-family: ${Nue.regular};
  @media (max-width: ${screenSizes.M}px) {
    min-width: 30px;
  }
`
export const NameValueUnitContainer = styled.div<any>`
  width: ${(props) => props.width};
  display: flex;
  align-items: center;
  height: ${(props) => (props.height ? props.height : '100%')};
  min-width: ${(props) => props.minWidth};
  margin: ${(props) => props.margin};
`

export const SingleFieldNameContainer = styled.div<any>`
  max-width: 812px;
  width: 100%;
  display: flex;
  flex-direction: ${(props) => props.flexDirection};
  /* gap: 25px; */
  align-items: ${(props) => (props.alignItem ? props.alignItem : 'center')};
  margin-top: ${(props) => props.marginTop};
  flex-wrap: wrap-reverse;
`

export const NameText = styled.p<any>`
  margin: ${(props) => (props.margin ? props.margin : 0)};
  font-size: 14px;
  font-family: ${Nue.regular};
  text-wrap: ${(props) => props.wrap};
`

export const NameDiv = styled.div<any>`
  padding: 6px 12px;
  background-color: ${colors.lightGrey3};
  border-radius: 4px 0px 0px 4px;
  border: 1px solid ${colors.lightGrey6};
  font-size: 14px;
  color: ${colors.lightGrey4};
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* width: 60px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis; */
  height: ${(props) => (props.height ? props.height : '100%')};
  min-width: ${(props) => (props.minWidth ? props.minWidth : '50px')};
  font-family: ${Nue.regular};
  @media (max-width: ${screenSizes.M}px) {
    min-width: 70px;
  }
`
export const AstricColor = styled.span`
  color: red;
`
export const TextAlign = styled.div<any>`
  text-align: ${(props) => props.position};
`
export const FlexWithGap = styled.div`
  display: flex;
  margin-right: 20px;
`
export const AlignItem = styled.div<any>`
  display: flex;
  align-items: center;
  margin-left: ${(props) => props.marginLeft};
  margin: ${(props) => props.margin};

  &.input {
    margin-top: 10px;
    & > div {
      width: 100%;

      & > div {
        height: 45px;
      }
    }
    input {
      flex: 1;
      height: 45px;
    }
  }
`
export const IconContainer = styled.div`
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  background-color: ${colors.white};
  border: 0.5px outset ${colors.darkGrey};
  transition: all 0.3s ease 0s;
  text-align: center;
  svg {
    width: 18px;
    height: 18px;
  }
  &.delete {
    border: 0.5px outset ${colors.errorRed};
    svg path {
      stroke: ${colors.errorRed};
    }
  }
  &.edit {
    border: 0.5px outset ${colors.blueLight};
    svg path {
      stroke: ${colors.blueLight};
    }
  }
  :hover {
    transform: translateY(-4px);
  }
`
export const RadioSkeleton = styled.span<any>`
  width: 48px;
  height: 48px;
  border: 5px solid ${(props) => (props.color ? props.color : 'lightgrey')};
  border-bottom-color: transparent;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;

  @keyframes rotation {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
`
export const ErrorContainer = styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-start;
  /* position: absolute;
  top: 72px;
  left: 10px;
  z-index: 1; */
`
export const ErrorMsg = styled.div<any>`
  color: ${colors.errorRed};
  font-size: 10px;
  padding-top: ${({ paddingTop }) => (paddingTop ? paddingTop : '5px')};
  /* padding-bottom: 5px; */
  transition: all 0.3s ease;
`
export const SpaceBetween = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`

export const Warning = styled.span`
  color: red;
`
export const IButton = styled.div`
  width: 12px;
  text-align: center;
  font-size: 10px;
  border-radius: 100%;
  background-color: black;
  color: white;
  font-weight: 700;
  font-style: italic;
`
export const TooltipContainer = styled.div<any>`
  position: relative;
  display: inline-block;
  cursor: pointer;
  left: ${(props) => props.positionLeft};
  bottom: ${(props) => props.positionBottom};
  /* Tooltip content */
  .tooltip-content {
    visibility: hidden;
    width: ${(props) => (props.width ? props.width : '150px')};
    max-width: ${(props) => props.maxWidth};
    background-color: rgba(51, 51, 51, 0.8); /* 80% transparent background */
    color: #fff;
    font-size: ${(props) => props.fontSize};
    text-align: ${(props) => (props.textAlign ? props.textAlign : 'center')};
    border-radius: 4px;
    padding: ${(props) => (props.padding ? props.padding : '8px')};
    position: absolute;
    z-index: 100;
    /* bottom: 125%; */
    left: ${(props) => (props.positionLeftDecs ? props.positionLeftDecs : '50%')};
    top: ${(props) => (props.positionTopDecs ? props.positionTopDecs : 'unset')};
    bottom: ${(props) => (props.positionBottomDecs ? props.positionBottomDecs : '50%')};
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
    white-space: ${(props) => (props.whiteSpace ? props.whiteSpace : 'wrap')};
    pointer-events: none; /* Make tooltip not clickable */
  }
  /* .tooltip-content::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent #333 transparent;
  } */
  /* Show the tooltip on hover */
  &:hover .tooltip-content {
    visibility: visible;
    opacity: 1;
  }

  &.crew {
    .tooltip-content {
      bottom: 100%;
    }
  }

  &.piece {
    .tooltip-content {
      bottom: -70px;
    }
  }

  &.small {
    .tooltip-content {
      font-size: 13px;
    }
  }

  &.no-pointer {
    cursor: default;
  }
`
export const SimpleInput = styled.input<any>`
  border: ${(props) =>
    props.error
      ? `1px solid ${colors.lightRed1}`
      : props.small && !props.showBorder
      ? 'none'
      : `1px solid ${colors.darkGrey}`};
  box-sizing: border-box;
  border-radius: 8px;
  width: 100%;
  /* font-family: NunitoSansBold; */
  font-size: 16px;
  font-weight: 500;
  height: ${(props) => (props.height ? props.height : '52px')};
  padding: ${(props) => (props.padding ? props.padding : '20px 16px 8px')};
  &.textArea {
    height: 100px;
    resize: none;
  }
  margin-top: ${(props) => props.marginTop};
  background: ${(props) =>
    props.disabled ? `${colors.grey2}` : props.error ? `${colors.lightRed}` : `${colors.white}`};
  color: ${colors.lightBlack};
  cursor: ${(props) => props.disabled && 'not-allowed'};
  @media (min-width: ${screenSizes.M}px) {
    height: ${(props) => (props.height ? props.height : '52px')};
  }
`

export const InputFive = styled.div<any>`
  width: 100%;
  .label-float {
    width: 100%;
    position: relative;
    padding-top: ${(props) => (props.small ? '0' : '8px')};
    z-index: 0;
  }

  &.lastpass {
    input:not(:placeholder-shown) ~ label, // for lastpass
    input:focus ~ label {
      font-size: 12px;
      top: 9px;
      color: #121619;
      font-family: NueRegular;
    }
  }

  .label-float input {
    /* border: 1px solid lightgrey; */
    border-radius: 5px;
    outline: none;
    min-width: 200px;
    /* padding: 15px 20px; */

    transition: all 0.1s linear;
    -webkit-transition: all 0.1s linear;
    -moz-transition: all 0.1s linear;
    -webkit-appearance: none;
    /* background-color: ${(props) => (props.error ? `${colors.lightRed}` : `${colors.white}`)}; */
  }

  .label-float input:focus {
    border: 1px solid ${colors.lightBlue1};
    box-shadow: ${colors.lightBlue} 0px 0px 5px 0px;
    background-color: ${colors.white};
  }

  .label-float input::placeholder {
    color: transparent;
  }

  .label-float label {
    pointer-events: none;
    position: absolute;
    top: ${(props) => (props.size === 'small' ? 'calc(50% - 8px)' : 'calc(50% - 11px)')};
    left: ${(props) => (props.size === 'small' ? '5px' : '11px')};
    font-size: ${(props) => (props.fontSize ? props.fontSize : '16px')};
    transition: all 0.1s linear;
    -webkit-transition: all 0.1s linear;
    -moz-transition: all 0.1s linear;
    background-color: white;
    padding: ${(props) => (props.size === 'small' ? '2px' : '5px')};
    box-sizing: border-box;
    color: ${colors.lightBlack};
    background: transparent;
  }

  .label-float input:required:invalid + label {
    color: red;
  }
  .label-float input:focus:required:invalid {
    border: 2px solid red;
  }
  .label-float input:required:invalid + label:before {
    content: '*';
  }
  .label-float input:focus + label,
  .label-float input:not(:placeholder-shown) + label {
    font-size: ${(props) => (props.fontSize ? props.fontSize : '12px')};
    top: ${(props) => (props.size === 'small' ? '4px' : '9px')};
    color: ${colors.darkGrey};
  }

  &.password {
    /* .label-float input {
      border-radius: 4px 4px 0px 0px;
      background-color: ${colors.white};
    } */
    .label-float input:focus {
      border: 1px solid ${colors.lightBlue1};
      box-shadow: ${colors.lightBlue} 0px 0px 5px 0px;
      /* box-shadow: -3px -4px 3px 0px rgba(0, 0, 0, 0.05);
      -webkit-box-shadow: -3px -4px 3px 0px rgba(0, 0, 0, 0.05);
      -moz-box-shadow: -3px -4px 3px 0px rgba(0, 0, 0, 0.05);
      background-color: ${colors.white}; */
    }
  }

  input[type='time']::-webkit-calendar-picker-indicator {
    position: absolute;
    top: ${(props) => (props.size === 'small' ? '12px' : '22px')};
    right: 5px;
    cursor: pointer;
  }
`

// =============== New ===============

export const opacityAnimation = keyframes`
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
`

export const rotate = keyframes`

  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }


`

export const SectionTitle = styled.h1<{ margin?: string; textAlign?: string }>`
  font-family: ${Nue.regular};
  font-size: 2.125rem;
  font-weight: 500;
  margin: ${({ margin }) => (margin ? margin : 0)};
  text-align: ${(props) => props.textAlign};
  //changesMobile
  @media (max-width: ${screenSizes.M}px) {
    font-size: 1.125rem;
  }

  &.opportunity {
    font-size: 24px;
  }
`

export const SectionSubHeading = styled.h2`
  font-family: ${Nue.bold};
  font-size: 18px;
`
export const BlueEdit = styled.span`
  cursor: pointer;
  color: ${colors.darkBlue};
  font-family: ${Nue.medium};
`
export const SubTableContainer = styled.div<any>`
  margin: ${(props) => props.margin};
  width: ${(props) => props.width};
`

export const CheckboxZoneLabel = styled.label<any>`
  display: inline-block;
  margin: ${({ margin }) => margin || '2px'};
  padding: ${({ padding }) => padding || '1px 6px 1px 6px'};
  cursor: ${({ cursor }) => cursor || 'pointer'};
  border-radius: ${({ isCircle }) => (isCircle && '50%') || 'unset'};
  transition: background-color 0.4s ease;
  input {
    cursor: pointer;
    margin: 0;
  }
  &:hover {
    background-color: ${({ hoverColor }) => hoverColor || '#f0f0f0'};
  }
`

export const IconCode = styled.span`
  font-size: 24px;
  &.cross {
    rotate: 45deg;
  }
`

export const ModalContainer = styled(SettingModalContainer)``

export const ModalHeaderContainer = styled(SettingModalHeaderContainer)``

export const ModalHeader = styled.h5`
  margin: 0;
  line-height: 1.5;
  font-size: 20px;
  font-weight: 600;
  color: ${colors.darkGrey};
`

export const CrossContainer = styled.div`
  cursor: pointer;
  svg {
    width: 25px;
    height: 25px;
    svg path {
      stroke: ${colors.darkGrey};
    }
    :hover {
      svg path {
        stroke: ${colors.grey};
      }
      transform: scale(1.03);
    }
    transition: all 0.01s linear;
  }
`

export const ProgressLoader = styled.span<{ progress: number }>`
  width: ${(props) => props.progress}%;
  height: 4.8px;
  background: #41b957d6;
  display: inline-block;
  box-shadow: 0 0 10px rgba(51, 51, 51, 0.5);
  box-sizing: border-box;
  transition: width 0.5s ease-in-out;
`
