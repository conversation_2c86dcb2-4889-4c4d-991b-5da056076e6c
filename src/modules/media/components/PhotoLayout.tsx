import React, { useState } from 'react'
import * as Styled from './photoReport.style'
import Logo from '../../../assets/images/nhr.png'
import { dayjsFormat, hasValues } from '../../../shared/helpers/util'
import './photoReport.style.css'

const PhotoLayout = ({
  pages,
  createdBy,
  oppData,
  isPrintView,
  onCaptionChange,
  onTitleChange,
  onDateChange,
  captions,
  reportTitle,
  reportDate,
}: {
  pages: any[][]
  createdBy: string
  isPrintView?: boolean
  onCaptionChange?: (mediaId: string, value: string) => void
  onTitleChange?: (value: string) => void
  onDateChange?: (value: string) => void
  captions: Record<string, string>
  reportTitle: string
  reportDate: string

  oppData: any
}) => {
  return (
    <div id="print">
      {pages.map((pageImages, pageIndex) => (
        <div className={`report-page ${isPrintView ? 'print-view' : ''}`} key={pageIndex}>
          <div className="header no-break">
            <div className="logo-section">
              <img src={Logo} alt="NHR logo" className="logo" />
            </div>

            <div className="title-section">
              {isPrintView ? (
                <span className="editable-title">{reportTitle}</span>
              ) : (
                <input
                  className="editable-title"
                  value={reportTitle}
                  onChange={(e) => onTitleChange?.(e.target.value)}
                />
              )}

              {isPrintView ? (
                <span className="editable-date">{dayjsFormat(reportDate, 'M-D-YYYY')}</span>
              ) : (
                <input
                  className="editable-date"
                  type="date"
                  value={reportDate}
                  onChange={(e) => onDateChange?.(e.target.value)}
                />
              )}

              <p className="prepared-by">Prepared By: {createdBy}</p>
            </div>

            <div className="client-section">
              <p>{oppData?.clientName}</p>

              <p>{oppData?.street}</p>

              <p>
                {oppData?.city}, {oppData?.state} {oppData?.zip}
              </p>
            </div>
          </div>

          <div className="photo-grid">
            {pageImages.map((item: any, idx: number) => (
              <div key={item?._id} className="photo-item">
                <img src={item?.url} alt={`Project photo-${idx}`} className="photo" />

                {hasValues(captions) ? (
                  <>
                    {isPrintView ? (
                      <p className="caption-text">{captions[item?._id] || ''}</p>
                    ) : (
                      <textarea
                        className="caption"
                        value={captions[item?._id] || ''}
                        onChange={(e) => onCaptionChange?.(item._id, e.target.value)}
                        placeholder="Enter text about the picture here"
                      />
                    )}
                  </>
                ) : null}
              </div>
            ))}
          </div>

          <div className="page-number">
            {pageIndex + 1} of {pages.length}
          </div>
        </div>
      ))}
    </div>
  )
}

export default PhotoLayout
