import { Form, Formik } from 'formik'
import * as SharedStyled from '../../../styles/styled'
import {
  RadioContainer,
  MarketingReportContainer,
  MetricCard,
  LabelText,
  Value,
  Cost,
  MetricsCont,
  ChartCont,
} from './style'
import { SharedDate } from '../../../shared/date/SharedDate'
import * as Styled from '../productionReport/style'
import Button from '../../../shared/components/button/Button'
import { useEffect, useMemo, useState } from 'react'
import { getMarketingReport } from '../../../logic/apis/report'
import { Root } from './data'
import {
  dayjsFormat,
  formatCurrencyNumber,
  formatDollarAmount,
  getFormattedRatio,
  getPastelColorFromId,
  hasValues,
} from '../../../shared/helpers/util'
import ReactApexChart from 'react-apexcharts'
import { ApexOptions } from 'apexcharts'
import SortableTable, { Column } from './components/SortableTable'
import RadioGroup from './components/RadioGroup'
import { useAppSelector } from '../../../logic/redux/reduxHook'
import { handleDateSelection } from '../runPayroll/NoncrewPayroll'
import GreenCheckSvg from '../../../assets/newIcons/greenCheck.svg'
import RedCheckSvg from '../../../assets/newIcons/redCheck.svg'

const srcMap = {
  Channel: 'channels',
  'Lead Source': 'leadSource',
  Campaign: 'campaign',
}

const tableColumns: Column[] = [
  { label: 'PO#', key: 'poNumber', isContact: true },
  { label: 'LEAD', key: 'lead', isDate: true },
  { label: 'OPP', key: 'opp', isDate: true },
  { label: 'NA', key: 'na', isDate: true },
  { label: 'PRES', key: 'pres', isDate: true },
  { label: 'SALE', key: 'sale', isDate: true },
  { label: 'VOL', key: 'vol', isCurrency: true },
  { label: 'GP', key: 'gp', isCurrency: true },
  { label: 'CAMPAIGN', key: 'campaign', link: true },
  { label: 'LEAD SOURCE', key: 'leadSource', link: true },
  { label: 'WEBPAGE', key: 'webpage' },
  { label: 'SEARCH TERM', key: 'searchTerm' },
  { label: 'AD GROUP', key: 'adGroup' },
]

const MarketingReport = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [buttonCall, setbuttonCall] = useState(false)
  const [initialValues, setInitialValues] = useState({
    startDate: '',
    endDate: '',
  })
  const [selectedLabel, setSelectedLabel] = useState('')

  const [marketingReportData, setMarketingReportData] = useState<Root>()

  const [selectedSrc, setSelectedSrc] = useState('Channel')
  const [selectedType, setSelectedType] = useState('Volume')

  const srcOptions = ['Channel', 'Lead Source', 'Campaign']
  const typeOptions = ['Volume', 'Sales', 'Leads']
  const [showOppsData, setShowOppsData] = useState(false)
  const [isSales, setIsSales] = useState(false)
  const globalSelector = useAppSelector((state: any) => state)
  const { currentCompany } = globalSelector.company
  const [selectedMetricOpps, setSelectedMetricOpps] = useState([])

  const handleLinkClick = (value: any, key: string) => {
    if (key === 'leadSource') {
      setSelectedSrc('Lead Source')
      setSelectedMetricOpps([])
      setSelectedLabel(value)
    } else {
      setSelectedMetricOpps([])
      setSelectedSrc('Campaign')
      setSelectedLabel(value)
    }
  }

  useEffect(() => {
    const params = new URLSearchParams(location.search)
    const paramStartDate = params.get('startDate')
    const paramEndDate = params.get('endDate')
    if (paramStartDate && paramEndDate) {
      if (paramStartDate && paramEndDate && currentCompany?._id) {
        setInitialValues({ startDate: paramStartDate, endDate: paramEndDate })
        if (!buttonCall) {
          handleSubmitForm({ startDate: paramStartDate, endDate: paramEndDate })
        }
      }
    }
  }, [location.search, currentCompany])

  const getSeriesData = (selectedType: string) => {
    switch (selectedType) {
      case 'Volume':
        return marketingReportData?.[srcMap[selectedSrc]]?.map((item: any) => item?.totalBudget)
      case 'Sales':
        return marketingReportData?.[srcMap[selectedSrc]]?.map((item: any) => item?.checkpointCounts?.Sale?.count)

      case 'Leads':
        return marketingReportData?.[srcMap[selectedSrc]]?.map(
          (item: any) => item?.checkpointCounts?.['New Lead']?.count
        )
    }
  }

  const getChartOptions = (seriesData: number[], labels: string[]) => {
    return {
      series: seriesData,

      options: {
        labels: labels,

        chart: {
          events: {
            dataPointSelection(_e: any, _chart: any, config: any) {
              const label = labels[config.dataPointIndex]
              setSelectedMetricOpps([])
              setSelectedLabel(label)
            },
          },
        },

        plotOptions: {
          pie: {
            dataLabels: {
              minAngleToShowLabel: 0,
              offset: 20,
            },
          },
        },

        states: {
          active: {
            filter: {
              type: 'darken',
            },
          },

          hover: {
            filter: {
              type: 'darken',
            },
          },
        },

        legend: {
          position: 'top',
          horizontalAlign: 'left',
          fontSize: '16px',
          itemMargin: {
            horizontal: 16,
          },
        },
      } as ApexOptions,
    }
  }

  const calculatedSrcValues = useMemo(() => {
    return marketingReportData?.[srcMap[selectedSrc]]?.reduce(
      (acc, item) => {
        return {
          Volume: acc?.Volume + item?.totalBudget,
          Sales: acc?.Sales + item?.checkpointCounts?.Sale?.count,
          Leads: acc?.Leads + item?.checkpointCounts?.['New Lead']?.count,
          name: [...acc?.name, item?.name],
        }
      },
      {
        Volume: 0,
        Sales: 0,
        Leads: 0,
        name: [],
      }
    )
  }, [marketingReportData, selectedSrc, selectedType])

  const [chartOptions, setChartOptions] = useState({})

  useEffect(() => {
    if (calculatedSrcValues?.name?.length) {
      const chartData = getChartOptions(getSeriesData(selectedType), calculatedSrcValues?.name)

      setChartOptions(chartData)
    }
  }, [calculatedSrcValues, selectedType])

  const handleSubmitForm = async (values: any) => {
    setMarketingReportData(undefined)
    setSelectedLabel('')
    setSelectedMetricOpps([])

    handleDateSelection(values.startDate, values.endDate)
    const paramsData: any = {
      endDate: values.endDate,
      startDate: values.startDate,
    }
    try {
      setIsLoading(true)
      const response = await getMarketingReport(paramsData)
      setMarketingReportData(response?.data?.report)
    } catch (error) {
      console.log('Error', error)
    } finally {
      setIsLoading(false)
    }
  }

  const selectedLsData = marketingReportData?.[srcMap[selectedSrc]]?.find((item: any) => item?.name === selectedLabel)

  const convData = selectedLsData?.conversion

  const metrics = useMemo(() => {
    return [
      {
        label: 'Visits',
        value: null,
        cost: formatDollarAmount(selectedLsData?.totalCost),
        oppIds: selectedLsData?.oppIds,
      },
      {
        label: 'Leads',
        value: selectedLsData?.checkpointCounts?.['New Lead']?.count,
        cost:
          selectedLsData?.totalCost && selectedLsData?.checkpointCounts?.['New Lead']?.count
            ? formatDollarAmount(selectedLsData?.totalCost! / selectedLsData?.checkpointCounts?.['New Lead']?.count!)
            : '--',
        oppIds: selectedLsData?.checkpointCounts?.['New Lead']?.opps,
      },
      {
        label: 'Opps',
        value: selectedLsData?.checkpointCounts?.['New Opportunity']?.count,
        cost:
          selectedLsData?.totalCost && selectedLsData?.checkpointCounts?.['New Opportunity']?.count
            ? formatDollarAmount(
                selectedLsData?.totalCost! / selectedLsData?.checkpointCounts?.['New Opportunity']?.count!
              )
            : '--',
        oppIds: selectedLsData?.checkpointCounts?.['New Opportunity']?.opps,
      },
      {
        label: 'NA',
        value: selectedLsData?.checkpointCounts?.['Needs Assessment']?.count,
        cost:
          selectedLsData?.totalCost && selectedLsData?.checkpointCounts?.['Needs Assessment']?.count
            ? formatDollarAmount(
                selectedLsData?.totalCost! / selectedLsData?.checkpointCounts?.['Needs Assessment']?.count!
              )
            : '--',

        oppIds: selectedLsData?.checkpointCounts?.['Needs Assessment']?.opps,
      },
      {
        label: 'Pres',
        value: selectedLsData?.checkpointCounts?.Presentation?.count,
        cost:
          selectedLsData?.totalCost && selectedLsData?.checkpointCounts?.Presentation?.count
            ? formatDollarAmount(selectedLsData?.totalCost! / selectedLsData?.checkpointCounts?.Presentation?.count!)
            : '--',
        oppIds: selectedLsData?.checkpointCounts?.Presentation?.opps,
      },
      {
        label: 'Sales',
        value: selectedLsData?.checkpointCounts?.Sale?.count,
        cost:
          selectedLsData?.totalCost && selectedLsData?.checkpointCounts?.Sale?.count
            ? formatDollarAmount(selectedLsData?.totalCost! / selectedLsData?.checkpointCounts?.Sale?.count!)
            : '--',
        oppIds: selectedLsData?.checkpointCounts?.Sale?.opps,
      },
      {
        label: 'Volume',
        value: selectedLsData?.totalBudget,
        cost: getFormattedRatio(selectedLsData?.totalBudget!, selectedLsData?.totalCost!),
        isValueCurrency: true,
        ratioLabel: 'Volume : Cost',
        oppIds: selectedLsData?.oppIds,
      },
      {
        label: 'Gross Profit',
        value: selectedLsData?.totalGrossProfit,
        cost: getFormattedRatio(selectedLsData?.totalGrossProfit!, selectedLsData?.totalCost!),
        isValueCurrency: true,
        ratioLabel: 'GP : Volume',
        oppIds: selectedLsData?.oppIds,
      },
    ]
  }, [selectedLabel, selectedSrc, selectedType])

  //use this  div at palce of poNumber just before po num value when isSale is true

  const oppsData = useMemo(() => {
    return marketingReportData?.opps
      ?.filter((item: any) => selectedMetricOpps?.includes(item?._id))
      ?.map((item: any) => ({
        poNumber: (
          <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
            {isSales &&
              'jobDone' in item &&
              (item.jobDone ? (
                <img src={GreenCheckSvg} alt="Completed" width="25" height="25" />
              ) : (
                <img src={RedCheckSvg} alt="Not Completed" width="25" height="25" />
              ))}
            <span>{`${item?.PO}-${item?.num}`}</span>
          </div>
        ),
        lead: item?.newLeadDate,
        opp: item?.oppDate,
        oppId: item?._id,
        stageGroup: item?.stageGroup,
        na: item?.needsAssessmentDate,
        pres: item?.presentationDate,
        sale: item?.saleDate,
        vol: item?.budget?.total,
        gp: item?.grossProfit,
        campaign: item?.campaign,
        leadSource: item?.leadSource,
        webpage: item?.webpage,
        searchTerm: item?.searchTerm,
        adGroup: item?.adGroup,
        contactName: item?.contactName,
      }))
  }, [selectedLabel, selectedMetricOpps])

  return (
    <MarketingReportContainer>
      <SharedStyled.SectionTitle margin="0 0 20px 0">Marketing Report</SharedStyled.SectionTitle>
      <Formik enableReinitialize={true} initialValues={initialValues} onSubmit={handleSubmitForm}>
        {({ values, setFieldValue, touched, errors }) => (
          <Form>
            <SharedStyled.FlexBox width="100%" gap="10px" alignItems="flex-end" alignItemsM="start" column="column">
              <div>
                <Styled.DateLabel>Date Start:</Styled.DateLabel>
                <div>
                  <SharedDate
                    value={values.startDate}
                    labelName="From"
                    stateName="startDate"
                    error={touched.startDate && errors.startDate ? true : false}
                    setFieldValue={setFieldValue}
                  />
                </div>
              </div>
              <div>
                <Styled.DateLabel>Date End:</Styled.DateLabel>
                <div>
                  <SharedDate
                    value={values.endDate}
                    labelName="To"
                    stateName="endDate"
                    error={touched.endDate && errors.endDate ? true : false}
                    min={values.startDate}
                    setFieldValue={setFieldValue}
                  />
                </div>
              </div>
              <Styled.KPIButtonContainer>
                <Button
                  type="submit"
                  isLoading={isLoading}
                  onClick={() => setbuttonCall(true)}
                  disabled={values.endDate === '' || values.startDate === ''}
                  width="max-content"
                  height="52px"
                >
                  Run Report
                </Button>
              </Styled.KPIButtonContainer>
            </SharedStyled.FlexBox>
          </Form>
        )}
      </Formik>

      {hasValues(marketingReportData) ? (
        <>
          <RadioContainer>
            <RadioGroup
              name="src"
              options={srcOptions}
              selected={selectedSrc}
              onChange={(val: string) => {
                setSelectedSrc(val)
                setSelectedLabel('')
              }}
            />
            <RadioGroup
              name="type"
              options={typeOptions}
              selected={selectedType}
              onChange={(val: string) => {
                setSelectedType(val)
              }}
            />
          </RadioContainer>

          {hasValues(calculatedSrcValues) && (
            <SharedStyled.FlexRow justifyContent="center">
              <SharedStyled.Text fontSize="20px">
                Total {selectedType}&nbsp;:&nbsp;
                {selectedType === 'Volume'
                  ? formatDollarAmount(calculatedSrcValues[selectedType as keyof typeof calculatedSrcValues])
                  : calculatedSrcValues[selectedType as keyof typeof calculatedSrcValues]}
              </SharedStyled.Text>
            </SharedStyled.FlexRow>
          )}

          <ChartCont id="chart" labelName={selectedLabel ? encodeURIComponent(selectedLabel) : ''}>
            {hasValues(chartOptions) && (
              <ReactApexChart
                key={JSON.stringify(chartOptions)}
                options={{
                  ...chartOptions?.options,
                  colors: calculatedSrcValues?.name?.map((name) => getPastelColorFromId(name)),
                }}
                series={chartOptions?.series}
                type="pie"
                height={500}
              />
            )}
          </ChartCont>

          <SharedStyled.FlexRow justifyContent="center" margin="0 0 20px 0">
            {selectedLabel ? (
              <SharedStyled.Text fontSize="20px">
                {selectedSrc?.replace(' ', '')} : {selectedLabel}
              </SharedStyled.Text>
            ) : (
              <div />
            )}
          </SharedStyled.FlexRow>

          <SharedStyled.FlexCol gap="10px">
            {Object?.entries?.(convData || {})?.length ? (
              <SharedStyled.FlexBox width="100%" justifyContent="space-around">
                <SharedStyled.Text fontWeight="600" fontSize="16px">
                  Lead Conversion: {convData?.['New Lead']?.['New Lead > New Opportunity']}%
                </SharedStyled.Text>

                <SharedStyled.Text fontWeight="600" fontSize="16px">
                  Opportunity Conversion: {convData?.['New Opportunity']?.['New Opportunity > Presentation']}%
                </SharedStyled.Text>

                <SharedStyled.Text fontWeight="600" fontSize="16px">
                  Sales Ratio: {convData?.['Presentation']?.['Presentation > Sale']}%
                </SharedStyled.Text>
              </SharedStyled.FlexBox>
            ) : null}

            <SharedStyled.FlexBox width="100%" justifyContent="center" alignItems="flex-start">
              {Object?.entries?.(convData || {})?.map(([stage, transitions], idx) => (
                <SharedStyled.FlexCol key={idx}>
                  {Object?.entries?.(transitions || {})?.map(([transition, value]) => (
                    <SharedStyled.Text fontSize="14px" key={transition}>
                      {transition}: {value}%
                    </SharedStyled.Text>
                  ))}
                </SharedStyled.FlexCol>
              ))}
            </SharedStyled.FlexBox>
          </SharedStyled.FlexCol>

          <MetricsCont>
            {selectedLsData &&
              metrics.map((metric, idx) => (
                <MetricCard
                  key={idx}
                  onClick={() => {
                    if (metric?.oppIds?.length) {
                      setSelectedMetricOpps(metric.oppIds)
                      setShowOppsData(true)
                      setIsSales(metric?.label === 'Sales')
                    }
                  }}
                  style={{ cursor: 'pointer', pointerEvents: metric?.oppIds?.length ? 'inherit' : 'none' }}
                >
                  {console.log({ metric })}
                  <LabelText>{metric.label}</LabelText>
                  {metric.isValueCurrency ? (
                    <>
                      <SharedStyled.TooltipContainer
                        positionLeft="0"
                        positionBottom="0"
                        positionLeftDecs="40px"
                        positionBottomDecs="40px"
                      >
                        <span className="tooltip-content">${metric.value?.toLocaleString() || '--'}</span>
                        <Value>${formatCurrencyNumber(metric.value)}</Value>
                      </SharedStyled.TooltipContainer>
                    </>
                  ) : (
                    <Value>{metric.value ?? '--'}</Value>
                  )}

                  {metric.isValueCurrency ? (
                    <div>
                      <SharedStyled.TooltipContainer
                        positionLeft="0"
                        positionBottom="0"
                        positionLeftDecs="40px"
                        positionBottomDecs="40px"
                      >
                        <span className="tooltip-content">{metric.ratioLabel}</span>
                        <Cost>{metric.cost ?? '--'}</Cost>
                      </SharedStyled.TooltipContainer>
                    </div>
                  ) : (
                    <Cost>{metric.cost ?? '--'}</Cost>
                  )}
                </MetricCard>
              ))}
          </MetricsCont>

          <SharedStyled.FlexRow margin="30px 0 0 0">
            {showOppsData && selectedLabel && oppsData?.length ? (
              <SortableTable
                data={oppsData}
                columns={tableColumns}
                onLinkClick={handleLinkClick}
                defaultSort={{ key: 'leads', direction: 'desc' }}
                className="marketing-table"
              />
            ) : null}
          </SharedStyled.FlexRow>
        </>
      ) : null}
    </MarketingReportContainer>
  )
}

export default MarketingReport
