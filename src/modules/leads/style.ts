import styled from 'styled-components'
import { colors, screenSizes } from '../../styles/theme'
import { Nue } from '../../shared/helpers/constants'
import { SettingModalHeaderContainer } from '../units/components/newUnitModal/style'
import { Field } from 'formik'
import { FlexCol, FlexRow, InputFive } from '../../styles/styled'

export const SalesContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  place-items: start;
  /* max-width: 1280px; */
  width: 100%;
  height: 100vh;
  overflow: hidden;

  padding-bottom: 10px;
  @media (min-width: 2000px) {
    margin: 0 auto;
  }

  .filter {
    .filterCont {
      justify-content: flex-start;

      & > div:first-child {
        width: max-content;
      }
    }

    @media (max-width: ${screenSizes.S}px) {
      flex-wrap: wrap;

      .filterCont {
        width: 100%;
      }
    }
  }

  max-height: calc(100vh - 88px);
  margin-top: -10px;
  margin-bottom: -24px;

  .indiana-scroll-container {
    & > div {
      max-height: calc(100vh - 250px);
    }
  }

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
`
export const SearchBarContainer = styled.div`
  width: 100%;
  display: flex;
  align-items: center;

  &.search-loader {
    position: relative;

    span {
      border: 3px solid #03a9f4;
      border-bottom-color: #75757550;
      left: unset;
      right: 2%;
    }
  }
`

export const SearchInput = styled.input`
  padding: 8px 16px;
  font-size: 16px;
  /* max-width: 320px; */
  width: 100%;
  border: none;
  outline: none;

  border-radius: 8px;
  border: 1px solid ${colors.lightGray};
  background: ${colors.lightGray};
`

export const SearchButton = styled.button`
  height: 40px;
  width: 48px;
  background: ${colors.darkGrey};
  border: none;
  border: 0.5px outset ${colors.grey3};
  border-left: none;
  cursor: pointer;
  svg {
    width: 16px;
    height: 16px;
    stroke: ${colors.white};
  }
  :hover {
    svg {
      transform: scale(1.2);
      transition: 0.3s ease-in-out;
    }
  }
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
`
export const DropDownOuterContainer = styled.div<{ isSearchActive?: boolean }>`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
`
export const DropDownContainer = styled.div<any>`
  position: relative;
  width: 100%;
`

export const DropDownContentContainer = styled.div<{ visibility?: boolean; height?: string }>`
  display: ${(props) => (props.visibility ? 'block' : 'none')};
  position: absolute;
  border: 1px solid ${colors.darkGrey};
  width: 100%;
  max-height: ${(props) => (props.height ? props.height : 'fit-content')};
  top: 39px;
  left: 0px;
  border-radius: 8px;
  overflow-y: auto;
  background: ${colors.white};
  z-index: 10;

  &.height {
    max-height: 400px;
  }
`

export const DropDownItem = styled.div<any>`
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  width: 100%;
  background: ${(props) => (props.active ? `${colors.darkGrey}` : `${colors.white}`)};
  color: ${(props) => (props.active ? `${colors.white}` : `${colors.darkGrey}`)};
  :hover {
    background: ${(props) => (props.noHover ? '' : `${colors.darkGrey}`)};
    color: ${(props) => (props.noHover ? '' : ` ${colors.white}`)};
  }
`

export const BoardContainer = styled.div<any>`
  /* max-width: 1280px; */
  width: 100%;
  margin-top: ${(props) => props.marginTop};
  height: fit-content;
  overflow-x: auto;
  @media (min-width: 2000px) {
    margin: 0 auto;
  }
`

export const StagesBoard = styled.div`
  min-width: 250px;
  padding: 13px 0px 10px 0px;
  border-radius: 6px;
  background: ${colors.lightGray1};
  margin: 0 10px 10px 10px;

  max-height: calc(100vh - 200px);
  overflow-y: scroll;
  padding-top: 0;

  &:first-child {
    margin-left: 0;
  }

  .heading {
    font-size: 14px;
    font-family: ${Nue.bold};
    margin: 0;
    padding-left: 10px;
    /* margin-bottom: 10px; */
    line-height: 40px;
    border-radius: 6px 6px 0px 0px;
    position: sticky;
    top: 0;
    z-index: 8;
    background: #f6f6f6;
  }
  .list-container {
    min-height: calc(100vh - 225px);
    overflow-y: auto;
    padding: 0px;
    margin: 0;
    /* margin-left: 10px; */
    display: flex;
    flex-direction: column;
    padding-top: 14px;
    gap: 14px;
  }

  &.loading {
    padding: 10px;
  }
`
export const ListItem = styled.li<any>`
  border-radius: 6px;
  list-style: none;
  padding: 10px;
  background: white;
  cursor: pointer;
  margin-right: 10px;
  margin-left: 10px;
  position: relative;
  min-height: 47px;

  border: 1px solid
    ${(props) =>
      props.borderColor === 'list-group-item-light'
        ? 'transparent'
        : props.borderColor === 'list-group-item-warning'
        ? '#ffe085'
        : props.borderColor === 'list-group-item-danger'
        ? '#ff6170'
        : props.borderColor === 'list-group-item-success'
        ? '#60d17a'
        : props.borderColor === 'list-group-item-info'
        ? '#58c7d9'
        : 'transparent'};

  box-shadow: 0 0 8px 2px
    ${(props) =>
      props.borderColor === 'list-group-item-light'
        ? 'transparent'
        : props.borderColor === 'list-group-item-warning'
        ? '#ffe085'
        : props.borderColor === 'list-group-item-danger'
        ? '#ff6170'
        : props.borderColor === 'list-group-item-success'
        ? '#60d17a'
        : props.borderColor === 'list-group-item-info'
        ? '#58c7d9'
        : 'transparent'};

  ::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 2px;
    width: ${(props) =>
      props.percent
        ? `${Math.min(props.percent, 100)}%`
        : '0%'}; /* Adjust this value to set the length of the border */
    height: 2px; /* Set the height of the border */
    background-color: green; /* Set the color of the border */
    z-index: 1;
    border-radius: 25px;
  }

  ${(props) =>
    props.percent >= 0 &&
    `
    ::after {
      content: '';
      position: absolute;
      top: -5px; /* Adjust this value to vertically position the circle */
      left: calc(${Math.min(props.percent, 100)}% - 2px); /* Adjust this value to horizontally position the circle */
      width: 8px; /* Diameter of the circle */
      height: 8px; /* Diameter of the circle */
      background-color: green;
      border-radius: 50%; /* Make the element a circle */
      z-index: 1; /* Ensure the circle appears above the progress line */
    }
  `}

  :hover {
    background: rgba(3, 3, 3, 0.1);
  }

  h3 {
    font-size: 12px;
    font-family: ${Nue.regular};
    text-transform: capitalize;
  }

  p {
    color: ${colors.gray1};
    font-family: ${Nue.medium};
    font-size: 12px;
    line-height: 16px;
    margin-top: 4px;
    margin-bottom: 20px;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 200px;
    overflow-x: hidden;
  }

  .aging-value {
    span {
      font-size: 12px;
      color: #6a747e;
      font-family: ${Nue.medium};
    }
  }
`

export const ModalContainer = styled.div<any>`
  backdrop-filter: ${(props) => (props.referrerModal ? 'blur(5px)' : 'unset')};
  background: ${colors.white};
  width: 670px;
  height: 100%;
  border-radius: 10px;
  @media (max-width: 768px) {
    width: 90vw;
  }
  min-height: 100vh;
`

export const ModalHeaderContainer = styled(SettingModalHeaderContainer)``

export const ModalHeader = styled.h5`
  margin: 0;
  line-height: 1.5;
  font-size: 20px;
  font-weight: 600;
  color: ${colors.darkGrey};
`
export const ModalHeaderInfo = styled.p`
  color: #757575;
  font-family: ${Nue.regular};
  font-size: 12px;
  text-align: left;
`

export const CrossContainer = styled.div`
  cursor: pointer;
  svg {
    width: 25px;
    height: 25px;
    svg path {
      stroke: ${colors.darkGrey};
    }
    :hover {
      svg path {
        stroke: ${colors.grey};
      }
      transform: scale(1.03);
    }
    transition: all 0.01s linear;
  }
`

export const NewLeadContainer = styled.div`
  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  width: 100%;
`

export const TextArea = styled(Field)<any>`
  width: ${(props) => (props.width ? props.width : '100%')};
  height: ${(props) => (props.height ? props.height : '48px')};
  margin-top: ${(props) => props.marginTop};
  cursor: pointer;
  resize: vertical;
  outline: none;
  border: 1px solid ${colors.lightGray};
  border-radius: 8px;
  padding: 12px 18px;
  max-height: 400px;
  min-height: 100px;
  :focus {
    border: 1px solid ${colors.lightBlue1};
    box-shadow: ${colors.lightBlue} 0px 0px 5px 0px;
  }
  margin-top: ${(props) => props.marginTop};
  font-family: ${Nue.regular};
  font-size: 16px;
`
export const AddressCont = styled(FlexRow)`
  align-items: flex-start;
  width: 100% !important;
  &.google {
    ${FlexCol} {
      & > div {
        width: 100%;
      }
    }
  }
  #street {
    width: 100%;
  }
  #city {
    width: 65%;
  }
  #state {
    width: 17%;
  }
  #zip {
    width: 18%;
    input {
      min-width: 115px;
    }
  }

  /* &#google {
    .label-float {
      padding-top: 0;

      input {
        padding-left: 7px;
      }

      label {
        left: 3px;
        top: calc(50% - 17px);
      }
    }
  } */

  ${InputFive} {
    width: 100%;
    input {
      padding: 20px 16px 8px 16px;
      width: inherit;
      font-family: ${Nue.medium};
    }
  }
`

export const AddressContainer = styled.div<any>`
  width: 100%;
  margin: 20px 0 0 0;

  .sub-heading {
    font-size: 16px;
  }
  .two-input {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 18px;

    @media (max-width: ${screenSizes.M}px) {
      flex-wrap: wrap;
    }
  }
  .inner-inputs {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
`

export const LabelDiv = styled.label<any>`
  font-size: 14px;
  font-weight: 500;
  color: ${colors.darkGrey};
  margin-top: ${(props) => props.marginTop};
  text-align: ${(props) => props.textAlign};
  width: ${(props) => props.width};
  cursor: ${(props) => props.cursor};
  @media (min-width: ${screenSizes.M}px) {
    font-size: 14px;
  }
  font-family: ${Nue.medium};
`
export const GoogleSearchBox = styled.div`
  width: 100%;
  .label-float {
  }
`

export const InactiveCont = styled.section<{ width?: string }>`
  --width: ${({ width }) => width};
  overflow-x: auto;
  width: 100%;
  height: 100%;
  max-width: calc(100vw - var(--width));
  .container {
    height: 100%;
  }

  .indiana-scroll-container {
    height: 100%;
    /* padding-right: 150px; */

    & > div {
      overflow-y: scroll;
    }
  }
`
