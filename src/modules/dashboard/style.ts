import styled from 'styled-components'
import { Nue } from '../../shared/helpers/constants'
import { colors, screenSizes } from '../../styles/theme'
import { rgba } from 'polished'
import { FlexCol, FlexRow } from '../../styles/styled'
import { TimeCardApproveNoApproveDiv } from '../../shared/timecard/style'

export const DashboardMainContainer = styled.div`
  width: 100%;
`
export const DashboardContainer = styled.div``

export const DashboardDropDownContainer = styled.div`
  margin: 10px 0;
`

export const DashboardDescription = styled.div<any>`
  font-size: 16px;
  color: ${({ color }) => color || '#000'};
  margin: ${({ margin }) => margin};
  font-family: ${Nue.regular};

  &.pointer {
    cursor: pointer;
  }

  a {
    color: ${colors.lightBlue1};
    cursor: pointer;
  }

  &.total {
    display: flex;
    flex-direction: column;
    width: max-content;
    p {
      display: grid;
      grid-template-columns: max-content max-content;
      justify-content: space-between;
      grid-column-gap: 14px;
    }
  }
`

export const DashboardTextContentWrapper = styled.div`
  margin: 20px 0;
  b {
    cursor: pointer;
  }

  li {
    ${DashboardDescription} {
      text-transform: capitalize;
      font-size: 14px;
      font-family: ${Nue.regular};
      margin-bottom: 4px;
    }
  }

  &.no-margin {
    margin: 0;
  }
`

export const DashboardHeading = styled.div<any>`
  font-size: 20px;
  margin: ${({ margin }) => margin};
  font-family: ${Nue.medium};
`
export const DashboardSubHeading = styled.div<any>`
  font-size: 16px;
  margin: ${({ margin }) => margin};
  font-family: ${Nue.medium};
  margin-bottom: -10px;
  margin-top: 20px;
`

export const TableContainer = styled.div`
  padding: 20px 0;
  width: 100%;
`

export const TableHeading = styled.div<any>`
  display: grid;
  grid-template-columns: max-content ${(props) =>
      props.gridTemplateColumns ? props.gridTemplateColumns : '1fr 2fr 1fr 1fr'};
  border: 1px solid #dddddd;
  color: ${colors.gray};
  font-family: ${Nue.medium};
  font-size: 11px;
  font-weight: 500;
  line-height: 20px;
  text-transform: uppercase;
  text-align: left;
  padding: 10px 12px;
  background: #f9f9f9;
  @media (min-width: ${screenSizes.S}px) {
    padding: 10px 24px;
  }

  &.action-list {
    color: ${colors.black};
    background: ${colors.white};
    border: none;
  }
`
export const TableTitle = styled.div`
  font-size: 14px;
  font-family: ${Nue.medium};
`

export const TableContent = styled.div<any>`
  display: grid;
  grid-template-columns: max-content ${(props) =>
      props.gridTemplateColumns ? props.gridTemplateColumns : '1fr 2fr 1fr 1fr'};
  padding: 8px 16px;
  color: ${colors.black};
  font-family: ${Nue.medium};
  font-size: 14px;
  border: 1px solid ${colors.lightGray};

  text-transform: capitalize;
  @media (min-width: ${screenSizes.S}px) {
    padding: 10px 24px;
  }

  &:hover {
    cursor: pointer;
    background: ${colors.grey2};
  }

  &.list-group-item-warning {
    background: ${rgba('#ffe085', 0.5)};
  }
  &.list-group-item-danger {
    background: #ffcece;
  }
  &.list-group-item-success {
    background: ${rgba('#60d17a', 0.5)};
  }
  &.list-group-item-info {
    background: ${rgba('#58c7d9', 0.5)};
  }

  &.checkbox {
    position: relative;
  }

  &.loading {
    grid-template-columns: repeat(4, 1fr);
  }
`
export const CrewReportTableContentLabel = styled.div`
  &.checkbox {
    input {
      position: absolute;
      left: 0;
    }
  }

  &.empty {
    padding: 10px 0;
    text-align: center;
    border: 1px solid #dddddd;
  }

  &.bold {
    font-family: ${Nue.medium};
    white-space: nowrap;
    width: max-content;
    overflow: hidden;
    max-width: 200px;
    text-overflow: ellipsis;
  }

  &.fit {
    display: flex;
    align-items: center;
    gap: 10px;

    .action-text {
      max-width: 356px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  &.light {
    color: #808080;
  }
`

export const ActionTag = styled.span<{ color: string }>`
  display: inline-block;
  text-align: center;
  background-color: ${colors.white};
  color: ${(props) => props.color};
  padding: 1px 0px 0px 0px;
  border-radius: 4px;
  font-size: 11px;
  min-width: 34px;
  width: 34px;
`

export const TableButtonWrapper = styled.div`
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 8px 0;
`
export const PrimaryButton = styled.button<any>`
  height: 40px;
  width: max-content;
  padding: 0 20px;
  outline: none;
  border: none;
  font-size: 16px;
  color: #fff;
  background-color: ${({ bgColor }) => bgColor};
  border-radius: 10px;
  cursor: pointer;
`

export const TableWrap = styled.table`
  width: 100%;
  border: 1px solid ${colors.lightGray};
  border-collapse: collapse;
  thead {
    border: 1px solid #dddddd;
    color: ${colors.gray};
    font-family: ${Nue.medium};
    font-size: 11px;
    font-weight: 500;
    line-height: 20px;
    text-transform: uppercase;
    text-align: left;
    background: #f9f9f9;
    @media (min-width: ${screenSizes.S}px) {
      padding: 10px 24px;
    }
  }
  th {
    text-align: left;
    padding: 10px 12px;
    font-size: 14px;
    font-family: ${Nue.medium};
    font-weight: 500;
  }

  tr {
    cursor: pointer;

    &:hover {
      background: ${colors.grey2};
    }
  }

  tbody {
    padding: 8px 16px;
    color: ${colors.black};
    font-family: ${Nue.medium};
    font-size: 14px;
    border: 1px solid ${colors.lightGray};

    text-transform: capitalize;
  }

  td {
    padding: 8px 16px;
    font-family: ${Nue.regular};

    @media (min-width: ${screenSizes.S}px) {
      padding: 10px 24px;
    }
  }

  @media (max-width: ${screenSizes.XS}px) {
    th {
      font-size: 12px;
    }
  }
`

export const DashboardWrap = styled.section`
  padding-bottom: 40px;

  p {
    font-family: ${Nue.regular};
  }
  h2 {
    font-size: 28px;
  }

  h4 {
    font-weight: 500;
    text-transform: capitalize;
  }

  ${TimeCardApproveNoApproveDiv} {
    max-width: 300px;
  }
`

export const MissingCont = styled.div`
  padding: 5px;
  background-color: #ffeeee;
  border-radius: 4px;
  font-size: 14px;
  font-family: ${Nue.medium};
  border: 1px solid #bbbbbb;
  width: 100%;
  text-align: center;

  &.red {
    background: #ff5656;
    color: #fff;
  }
`

export const SelectCont = styled(FlexRow)`
  margin: 10px 0;
  @media (min-width: ${screenSizes.M}px) {
    width: 40%;
  }
`

export const TableCont = styled.div`
  overflow-x: scroll;

  ${TableContainer} {
    width: 768px;
  }

  table {
    width: 768px;
  }
  @media (min-width: ${screenSizes.M}px) {
    overflow-x: hidden;
    ${TableContainer} {
      width: 100%;
    }
    table {
      width: 100%;
    }
  }
`

export const InfoCont = styled(FlexCol)`
  min-width: 200px;

  .half {
    grid-template-columns: max-content max-content !important;
  }

  .bold {
    p {
      font-family: ${Nue.medium};
      font-size: 16px;
    }
  }

  .center {
    justify-content: center !important;
  }

  p {
    font-size: 16px;

    &:last-child {
      padding-left: 10px;
    }
  }

  .heading {
    font-size: 16px;
    white-space: nowrap;
  }

  @media (min-width: ${screenSizes.XS}px) {
    .heading {
      font-size: 21px;
    }
  }
`
