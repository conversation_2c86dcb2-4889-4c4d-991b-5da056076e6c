import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { SLoader } from '../../../shared/components/loader/Loader'
import * as SharedStyled from '../../../styles/styled'
import { useSelector } from 'react-redux'
import * as Styled from '../../dashboard/style'
import CustomSelect from '../../../shared/customSelect/CustomSelect'
import Button from '../../../shared/components/button/Button'
import {
  dayjsFormat,
  extractPermissionByName,
  formatCurrency,
  formatDate,
  getEnumValue,
  hasValues,
  isSuccess,
  nextAction,
  notify,
  startOfDate,
} from '../../../shared/helpers/util'
import {
  assignOppsToSalesPerson,
  getDashboardActions,
  getDashboardSalesPersonActionList,
  getDashboardSalesPersonNoActionList,
  getSalesPersonReport,
} from '../../../logic/apis/dashboard'
import { renderLoadingTable } from '../../dashboard/components/dashboardLoaders'
import { Form, Formik } from 'formik'
import { ActionsContainer } from '../style'
import { getActionMembers } from '../../../logic/apis/sales'
import { Types } from '../../contact/constant'

const TaskColor: Record<string, string> = {
  Task: '#02952B',
  Call: '#0112C1',
  Text: '#faa010',
  Email: '#d90202',
}

const getTagColor = (type: string) => {
  return TaskColor[type]
}

const ActionsDashboard = () => {
  const navigate = useNavigate()

  const [salesData, setSalesData] = useState<{ [key: string]: any }>({})
  const [showMoreClicked, setShowMoreClicked] = useState(false)
  const [actionLimit, setActionLimit] = useState(10)
  const [actionData, setActionData] = useState<any>({})

  const globalSelector = useSelector((state: any) => state)
  const { currentMember, positionDetails, positionPermissions } = globalSelector.company
  const position = positionDetails?.symbol

  const hasOppManagedFullPermission =
    hasValues(positionDetails) && extractPermissionByName(positionDetails, 'actions')?.permissions < 3

  const [salesPersonDrop, setSalesPersonDrop] = useState<any[]>([])
  const [allOppsMembers, setAllOppsMembers] = useState([])
  const [salesPersonName, setSalesPersonName] = useState<string>('')
  const [assignSalesPerson, setAssignSalesPerson] = useState('')
  const [assignLoading, setAssignLoading] = useState(false)
  const [actionResultData, setActionResultData] = useState<any>([])
  const [noActionResultData, setNoActionResultData] = useState<any>([])
  const [contactActionData, setContactActionData] = useState<any>([])
  const [isShowNoAction, setIsShowNoAction] = useState<boolean>(false)
  const [salesDataLoading, setSalesDataLoading] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedOppsIds, setSelectedOppsIds] = useState<any[]>([])

  useEffect(() => {
    if (salesPersonName || positionDetails?.symbol === 'SalesPerson' || position == 'RRTech') {
      getDashboardSalesPersonAction()
      getActionData()
      getSalesReport()
    }
  }, [currentMember, positionDetails, salesPersonName, salesPersonDrop?.length])

  useEffect(() => {
    if ((currentMember?.name && hasOppManagedFullPermission) || position === 'SalesPerson' || position == 'RRTech') {
      setSalesPersonName(currentMember?.name)
    }
    if (position) {
      getPositions()
    }
  }, [position, currentMember?.name])

  const getSalesReport = async () => {
    const salesPerson =
      position !== 'SalesPerson' || position !== 'RRTech'
        ? salesPersonDrop.filter((value: any) => value.name === salesPersonName)
        : []
    try {
      const payloadId = position === 'SalesPerson' || position == 'RRTech' ? currentMember?._id : salesPerson[0]?._id
      if (payloadId) {
        const response = await getSalesPersonReport({
          salesPersonId: payloadId,
          currentDate: startOfDate(new Date()),
        })

        setSalesData(response?.data?.data?.report)
      }
    } catch (error) {
      console.log('Error', error)
    } finally {
      setSalesDataLoading(false)
    }
  }

  const getPositionMembers = async () => {
    try {
      const response = await getActionMembers({}, false)
      if (isSuccess(response)) {
        setSalesPersonDrop(response?.data?.data?.memberData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }
  const getTerminatedMembers = async () => {
    try {
      const response = await getActionMembers({
        hasOpportunity: true,
      })
      if (isSuccess(response)) {
        setAllOppsMembers(response?.data?.data?.memberData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('getTerminatedMembers', err)
    }
  }

  const getActionData = async () => {
    const salesPerson =
      position !== 'SalesPerson' || position !== 'RRTech'
        ? salesPersonDrop.filter((value: any) => value.name === salesPersonName)
        : []

    const payloadId = position === 'SalesPerson' || position == 'RRTech' ? currentMember?._id : salesPerson?.[0]?._id
    try {
      if (payloadId) {
        const actionRes = await getDashboardActions({
          salesPersonId: payloadId,
          endDate: startOfDate(new Date()),
        })

        setActionData(actionRes?.data?.data)
      }
    } catch (error) {
      console.error('Error=====>', error)
    }
  }

  const handleAssignClick = async () => {
    const filterId = salesPersonDrop.filter((value: any) => value.name === assignSalesPerson)
    try {
      setAssignLoading(true)
      const res = await assignOppsToSalesPerson(filterId[0]?._id, selectedOppsIds)
      if (isSuccess(res)) {
        notify('Opps assigned successfully!', 'success')
        getActionData()
        getDashboardSalesPersonAction()
        getSalesReport()
      }
    } catch (error) {
      console.log('Assign Opps Error', error)
    } finally {
      setAssignLoading(false)
      setSelectedOppsIds([])
    }
  }

  const getDashboardSalesPersonAction = async () => {
    const filterId =
      position !== 'SalesPerson' || position !== 'RRTech'
        ? salesPersonDrop.filter((value: any) => value.name === salesPersonName)
        : []
    try {
      !showMoreClicked && setIsLoading(true)
      const payloadId = position === 'SalesPerson' || position == 'RRTech' ? currentMember?._id : filterId[0]?._id

      if (payloadId) {
        const actionResult = await getDashboardSalesPersonActionList(payloadId, actionLimit)
        const noActionResult = await getDashboardSalesPersonNoActionList(payloadId)
        setActionResultData(actionResult?.data?.data?.currentOpps)
        setContactActionData(actionResult?.data?.data?.contacts)
        setNoActionResultData(noActionResult?.data?.data?.currentOpps)
      }
    } catch (error) {
      console.log('Error', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getPositions = async () => {
    try {
      getPositionMembers()
      getTerminatedMembers()
    } catch (err) {
      // notify('Something went wrong!', 'error')
      console.log('GET POSITION FAILED', err)
    }
  }

  useEffect(() => {
    if (actionLimit > 10) {
      getDashboardSalesPersonAction()
    }
  }, [actionLimit])

  const initialValue = {}

  return (
    <>
      {hasValues(positionPermissions) && !positionPermissions?.['to do list dashboard'] ? (
        <p>You do not have permission to view this dashboard.</p>
      ) : (
        <ActionsContainer>
          <Formik initialValues={initialValue} onSubmit={() => {}}>
            {({ setFieldValue }) => (
              <Form>
                <Styled.DashboardTextContentWrapper>
                  {hasOppManagedFullPermission ? (
                    <Styled.DashboardDropDownContainer
                      style={{
                        margin: '0px',
                      }}
                    >
                      <Styled.SelectCont>
                        <CustomSelect
                          labelName="Select team member"
                          error={false}
                          value={salesPersonName}
                          dropDownData={allOppsMembers?.map((val) => val.name)}
                          setValue={setSalesPersonName}
                          setFieldValue={setFieldValue}
                          innerHeight="45px"
                          margin="10px 0 0 0"
                          stateName="bVentCount"
                          disabled={!salesPersonDrop?.length}
                        />
                      </Styled.SelectCont>
                    </Styled.DashboardDropDownContainer>
                  ) : null}
                </Styled.DashboardTextContentWrapper>
                <Styled.DashboardTextContentWrapper className="no-margin">
                  <SharedStyled.FlexCol gap="16px" margin="0 0 10px 0">
                    {/* <Styled.DashboardHeading>Sales Action List</Styled.DashboardHeading> */}
                    <SharedStyled.FlexCol gap="2px">
                      {salesDataLoading ? (
                        <SLoader width={200} height={20} />
                      ) : (
                        <Styled.DashboardDescription>
                          Opportunities w/ Overdue Actions: {actionData?.numOverdue}
                        </Styled.DashboardDescription>
                      )}
                      {salesDataLoading ? (
                        <SLoader width={200} height={20} />
                      ) : (
                        <Styled.DashboardDescription>
                          Opportunities w/ No Action: {actionData?.numNoAction}
                        </Styled.DashboardDescription>
                      )}

                      {salesPersonName && (
                        <Styled.DashboardTextContentWrapper className="no-margin">
                          {salesDataLoading ? (
                            <SLoader width={150} height={10} />
                          ) : (
                            <Styled.DashboardDescription style={{ margin: '0 0 2px 0' }}>
                              Active Opportunities: {salesData?.activeOppsNum}
                            </Styled.DashboardDescription>
                          )}
                          {/*
                      {salesDataLoading ? null : (
                        <Styled.DashboardDescription
                          className={salesData?.firstHalf?.opps?.length ? 'pointer' : ''}
                          onClick={() => {
                            setShowNotCompleted((p) => !p)
                          }}
                          style={{
                            pointerEvents: salesData?.firstHalf?.opps?.length ? 'auto' : 'none',
                          }}
                        >
                          Sold But Not Completed: {salesData?.firstHalf?.num}
                        </Styled.DashboardDescription>
                      )}*/}
                        </Styled.DashboardTextContentWrapper>
                      )}

                      {/*    {showNotCompleted && salesPersonName && (
                    <Styled.TableWrap>
                      <thead>
                        {notCompletedColums?.map((item) => (
                          <th key={item}>{item}</th>
                        ))}
                      </thead>
                      <tbody>
                        {salesData?.firstHalf?.opps?.map((item: any) => (
                          <tr
                            onClick={() => {
                              navigate(`/${getEnumValue(item?.stage?.stageGroup)}/opportunity/${item?._id}`)
                            }}
                            key={item?._id}
                          >
                            <td>
                              {item?.clientId?.firstName} {item?.clientId?.lastName}
                            </td>
                            <td>{dayjsFormat(item?.saleDate, 'M/D/YY')}</td>
                            <td>${formatCurrency(item?.soldValue)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </Styled.TableWrap>
                  )}*/}
                    </SharedStyled.FlexCol>
                  </SharedStyled.FlexCol>
                </Styled.DashboardTextContentWrapper>
                <Styled.TableCont>
                  {salesPersonName && (
                    <>
                      <Styled.TableContainer>
                        <Styled.TableHeading className="action-list">
                          <Styled.TableTitle>&nbsp;</Styled.TableTitle>
                          <Styled.TableTitle>Contact</Styled.TableTitle>
                          <Styled.TableTitle>Action</Styled.TableTitle>
                          <Styled.TableTitle>Date</Styled.TableTitle>
                          <Styled.TableTitle>Stage</Styled.TableTitle>
                        </Styled.TableHeading>
                        {isLoading && <>{renderLoadingTable()}</>}
                        {!isLoading && actionResultData?.length ? (
                          [...actionResultData, ...contactActionData]
                            ?.sort((a: any, b: any) => {
                              const dateA = new Date(a?.nextAction?.due)?.getTime()
                              const dateB = new Date(b?.nextAction?.due)?.getTime()
                              return dateA - dateB
                            })

                            ?.map((value: any, idx: number) => (
                              <Styled.TableContent
                                onClick={() => {
                                  if (value?.type) {
                                    navigate(`/contact/profile/${value?._id}/false`)
                                  } else {
                                    navigate(`/${getEnumValue(value?.stage?.stageGroup)}/opportunity/${value?._id}`)
                                  }
                                }}
                                className={`checkbox ${nextAction(value)}`}
                                key={value?._id}
                              >
                                <Styled.CrewReportTableContentLabel className="checkbox">
                                  {hasOppManagedFullPermission ? (
                                    <input
                                      type="checkbox"
                                      checked={selectedOppsIds?.includes(value?._id)}
                                      onClick={(e) => {
                                        e.stopPropagation()
                                        if (selectedOppsIds?.includes(value?._id)) {
                                          setSelectedOppsIds(selectedOppsIds.filter((id) => id !== value?._id))
                                        } else {
                                          setSelectedOppsIds([...selectedOppsIds, value?._id])
                                        }
                                      }}
                                    />
                                  ) : null}
                                </Styled.CrewReportTableContentLabel>
                                <SharedStyled.TooltipContainer
                                  width="260px"
                                  positionLeft="4px"
                                  positionBottom="0px"
                                  positionLeftDecs="50%"
                                  positionBottomDecs="20px"
                                >
                                  <Styled.CrewReportTableContentLabel className="bold" id={`name-${idx}`}>
                                    {value?.contactId?.fullName || value?.fullName}
                                  </Styled.CrewReportTableContentLabel>

                                  <span className="tooltip-content">
                                    {value?.contactId?.fullName || value?.fullName}
                                  </span>
                                </SharedStyled.TooltipContainer>
                                <SharedStyled.TooltipContainer
                                  width="330px"
                                  positionLeft="4px"
                                  positionBottom="0px"
                                  positionLeftDecs="50%"
                                  positionBottomDecs="20px"
                                >
                                  <Styled.CrewReportTableContentLabel className="fit">
                                    <Styled.ActionTag color={getTagColor(value?.nextAction?.type)}>
                                      {value?.nextAction?.type}
                                    </Styled.ActionTag>

                                    <span id={`body-${idx}`} className="action-text">
                                      {value?.nextAction?.body}
                                    </span>

                                    {(document.getElementById(`body-${idx}`)?.offsetWidth as number) > 340 ? (
                                      <span className="tooltip-content">{value?.nextAction?.body}</span>
                                    ) : null}
                                  </Styled.CrewReportTableContentLabel>
                                </SharedStyled.TooltipContainer>
                                <Styled.CrewReportTableContentLabel className="light">
                                  {formatDate(value?.nextAction?.due)}
                                </Styled.CrewReportTableContentLabel>
                                <Styled.CrewReportTableContentLabel className="light">
                                  {value?.stage?.name ||
                                    `Contact: ${Object.entries(Types)?.find(([_, v]) => v === value?.type)?.[0] || ''}`}
                                </Styled.CrewReportTableContentLabel>
                              </Styled.TableContent>
                            ))
                        ) : (
                          <>
                            {!isShowNoAction && !isLoading && (
                              <Styled.CrewReportTableContentLabel className="empty">
                                No data found
                              </Styled.CrewReportTableContentLabel>
                            )}
                          </>
                        )}
                        {isShowNoAction &&
                          noActionResultData?.map((value: any) => (
                            <Styled.TableContent
                              onClick={() => {
                                navigate(`/${getEnumValue(value?.stage?.stageGroup)}/opportunity/${value?._id}`)
                              }}
                              className={`checkbox ${nextAction(value)}`}
                              key={value?._id}
                            >
                              <Styled.CrewReportTableContentLabel className="checkbox">
                                {hasOppManagedFullPermission && (
                                  <input
                                    type="checkbox"
                                    checked={selectedOppsIds?.includes(value?._id)}
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      if (selectedOppsIds?.includes(value?._id)) {
                                        setSelectedOppsIds(selectedOppsIds.filter((id) => id !== value?._id))
                                      } else {
                                        setSelectedOppsIds([...selectedOppsIds, value?._id])
                                      }
                                    }}
                                  />
                                )}
                              </Styled.CrewReportTableContentLabel>
                              <Styled.CrewReportTableContentLabel>
                                {value?.contactId?.fullName}
                              </Styled.CrewReportTableContentLabel>
                              <Styled.CrewReportTableContentLabel></Styled.CrewReportTableContentLabel>
                              <Styled.CrewReportTableContentLabel></Styled.CrewReportTableContentLabel>
                              <Styled.CrewReportTableContentLabel className="light">
                                {value?.stage?.name}
                              </Styled.CrewReportTableContentLabel>
                            </Styled.TableContent>
                          ))}
                      </Styled.TableContainer>
                      <Styled.TableButtonWrapper>
                        <Button
                          width="max-content"
                          onClick={() => {
                            setShowMoreClicked(true)
                            setActionLimit((prev) => {
                              return prev + 50
                            })
                          }}
                          disabled={!(actionLimit === actionResultData?.length)}
                        >
                          Show More Actions
                        </Button>

                        <Button width="max-content" bgColor="#E0A800" onClick={() => setIsShowNoAction(true)}>
                          Show Opps w/ No Actions
                        </Button>
                      </Styled.TableButtonWrapper>
                    </>
                  )}
                </Styled.TableCont>{' '}
                {hasOppManagedFullPermission ? (
                  <>
                    <Styled.DashboardSubHeading>Assign selected opps to:</Styled.DashboardSubHeading>
                    <Styled.DashboardDropDownContainer>
                      <Styled.SelectCont>
                        <CustomSelect
                          labelName="Select team member"
                          error={false}
                          value={assignSalesPerson}
                          dropDownData={salesPersonDrop
                            ?.filter((val) => val.name !== salesPersonName)

                            ?.map((val) => val.name)}
                          setValue={setAssignSalesPerson}
                          setFieldValue={setFieldValue}
                          innerHeight="45px"
                          disabled={!salesPersonDrop?.length}
                          margin="10px 0 0 0"
                          stateName="bVentCount"
                        />
                      </Styled.SelectCont>
                    </Styled.DashboardDropDownContainer>
                    <Button
                      width="max-content"
                      disabled={!selectedOppsIds?.length || !assignSalesPerson}
                      onClick={handleAssignClick}
                      isLoading={assignLoading}
                    >
                      Assign
                    </Button>
                  </>
                ) : null}
              </Form>
            )}
          </Formik>
        </ActionsContainer>
      )}
    </>
  )
}

export default ActionsDashboard
