import React from 'react'
import styled from 'styled-components'
import { FlexCol } from '../../../../../styles/styled'
import CustomSelect from '../../../../../shared/customSelect/CustomSelect'

const TeamMembersContainer = styled(FlexCol)`
  margin-top: 20px;
  width: 100%;
`

const TeamMembersHeader = styled.div`
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 15px;

  svg {
    margin-left: 5px;
    cursor: pointer;
  }
`

interface TeamMembersProps {
  values: any
  errors: any
  touched: any
  setFieldValue: (field: string, value: any) => void
  salesPersonDrop: any
  officeDrop: any
  projectManagerDrop: any
}

const TeamMembers: React.FC<TeamMembersProps> = ({
  values,
  errors,
  salesPersonDrop,
  officeDrop,
  projectManagerDrop,
  touched,
  setFieldValue,
}) => {
  return (
    <TeamMembersContainer>
      <CustomSelect
        labelName="Appointment Setter"
        stateName="appointmentSetter"
        error={touched.appointmentSetter && errors.appointmentSetter ? true : false}
        setFieldValue={setFieldValue}
        setValue={() => {}}
        value={values.appointmentSetter || ''}
        dropDownData={officeDrop?.map((item: any) => item.name) || []}
        innerHeight="52px"
        margin="10px 0"
      />

      <CustomSelect
        labelName="Sales Rep"
        stateName="salesRep"
        error={touched.salesRep && errors.salesRep ? true : false}
        setFieldValue={setFieldValue}
        setValue={() => {}}
        value={values.salesRep || ''}
        dropDownData={salesPersonDrop?.map((item: any) => item.name) || []}
        innerHeight="52px"
        margin="10px 0"
      />

      <CustomSelect
        labelName="Project Manager"
        stateName="projectManager"
        error={touched.projectManager && errors.projectManager ? true : false}
        setFieldValue={setFieldValue}
        setValue={() => {}}
        value={values.projectManager || ''}
        dropDownData={projectManagerDrop?.map((item: any) => item.name) || []}
        innerHeight="52px"
        margin="10px 0"
      />
    </TeamMembersContainer>
  )
}

export default TeamMembers
