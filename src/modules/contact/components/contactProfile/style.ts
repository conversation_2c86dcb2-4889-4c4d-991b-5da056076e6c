import { Field } from 'formik'
import styled from 'styled-components'
import { colors, screenSizes } from '../../../../styles/theme'
import { SettingsCont } from '../../../units/style'
import { ButtonContainer } from '../../../../styles/styled'
import { TableOuterContainer } from '../../../../shared/table/style'
import { Nue } from '../../../../shared/helpers/constants'

export const ClientProfileContainer = styled(SettingsCont)`
  width: 100%;
  .form {
    width: 100%;
    height: 100%;
  }

  h1 {
    margin-right: auto;
    margin-bottom: 10px;
  }

  ${ButtonContainer} {
    @media (max-width: ${screenSizes.S}px) {
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }

  @media (min-width: ${screenSizes.M}px) {
    width: 55%;
  }

  .link {
    cursor: pointer;
    color: ${colors.darkBlue};
  }

  .tag-option {
    border: 1px solid lightgrey;
    padding: 5px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    background-color: ${colors.lightGrey3};
    gap: 5px;
  }
`

export const LabelDiv = styled.label<any>`
  font-size: 14px;
  font-weight: 500;
  color: ${colors.darkGrey};
  margin-top: ${(props) => props.marginTop};
  text-align: ${(props) => props.textAlign};
  width: ${(props) => props.width};
  cursor: ${(props) => props.cursor};
  @media (min-width: ${screenSizes.M}px) {
    font-size: 16px;
  }
`

export const TextArea = styled(Field)<any>`
  width: ${(props) => (props.width ? props.width : '100%')};
  height: ${(props) => (props.height ? props.height : '48px')};
  margin-top: ${(props) => props.margintop};
  cursor: pointer;
  resize: vertical;
  outline: none;
  border: 1px solid ${colors.lightGray};
  border-radius: 8px;
  padding: 12px 18px;
  :focus {
    border: 1px solid ${colors.lightBlue1};
    box-shadow: ${colors.lightBlue} 0px 0px 5px 0px;
  }
  margin-top: ${(props) => props.margintop};

  font-family: ${Nue.medium};
  font-weight: 500;
  font-size: 16px;
  color: rgb(58, 59, 65);
`

export const HeaderDiv = styled.p<any>`
  margin: 0;
  width: 100%;
  font-size: 16px;
  font-weight: 500;
  color: ${colors.darkGrey};
  margin-top: ${(props) => props.marginTop};
  text-align: ${(props) => props.textAlign};
  width: ${(props) => props.width};
  cursor: ${(props) => props.cursor};
  @media (min-width: ${screenSizes.M}px) {
    font-size: 22px;
  }
`

export const OpportunityContainer = styled.div<any>`
  margin-top: ${(props) => props.marginTop};
  width: ${(props) => props.width};

  ${TableOuterContainer} {
    @media (min-width: ${screenSizes.M}px) {
      &::-webkit-scrollbar {
        display: none;
      }

      /* Hide scrollbar for IE, Edge and Firefox */

      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
    }
  }
`

export const ContactContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  .todo-comments {
    @media (min-width: ${screenSizes.M}px) {
      width: 45%;
    }
    width: 100%;
  }
  @media (max-width: ${screenSizes.M}px) {
    flex-direction: column;
  }
`

export const TodoNextCont = styled.div`
  .strike {
    span {
      text-decoration: line-through;
    }

    .pill-text {
      text-decoration: none !important;
    }
  }
`

export const ActionHistoryCont = styled.div`
  overflow-y: scroll;
  max-height: 380px;
  width: 100%;
`
